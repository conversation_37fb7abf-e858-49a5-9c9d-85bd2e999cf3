from time import sleep
from celery import shared_task
from celery_progress.backend import ProgressRecorder
from school.models import Level, Year, Enrollment, PDFFile
from exams.grades_utils import update_level_term_result
from exams.models import (
    SchoolTerm, LevelSubject, SchoolTerm, Grade, TermResult, 
    Term)
from users.models import CustomUser
from django.db.models import Prefetch, OuterRef, Subquery, Count
from main.utils import (
    EDUCATION_FRENCH, CYCLE_PRIMARY,  CYCLE_BOTH,
    CYCLE_SECONDARY, EDUCATION_ARABIC)
from users.models import CustomUser
from . import reports, views

import os

@shared_task(bind=True)
def go_to_sleep(self, duration):
    recorder = ProgressRecorder(self)
    total = 10
    for i in range(total):
        sleep(duration)
        recorder.set_progress(i + 1, total, f'Progression: {i} sur {total}')
    return 'Done'


@shared_task(bind=True)
def update_levels_results_task(self, user_id, year_id, term_id, education):
    recorder = ProgressRecorder(self)

    user = CustomUser.objects.get(id=user_id)
    year = Year.objects.get(id=year_id)
    term = SchoolTerm.objects.get(id=term_id)
    generic_term_id = term.term_id
    queryset = Level.objects.for_user(
        user=user, year=year, education=term.education,
        with_education=True).filter(generic_level__schoolterm__term__id=generic_term_id).distinct()
    total = queryset.count()
    
    for i, level in enumerate(queryset):
        term_obj = SchoolTerm.objects.filter(
            level__id=level.generic_level_id, 
            year__id=term.year_id, term__id=generic_term_id,
            school__id=user.school_id).first()
        update_level_term_result(user, level, term_obj)
        recorder.set_progress(i + 1, total, f'{round(i * 100 / total)} %')


@shared_task(bind=True)
def create_grades_for_new_subject(self, school_id, year_id, 
                                  generic_level_id, subject_id):
    # Creates empty grade objects for students when a new subject is added
    subject = LevelSubject.objects.select_related('subject').get(pk=subject_id)
    education = subject.subject.education
    enrollments = Enrollment.objects.filter(
            school__id=school_id, year__id=year_id)

    if education == EDUCATION_FRENCH:
        enrollments = enrollments.filter(generic_level_fr__id=generic_level_id).only('id')
    else:
        enrollments = enrollments.filter(generic_level_ar__id=generic_level_id).only('id')
    
    terms = SchoolTerm.objects.filter(year__id=year_id, level__id=generic_level_id,
                                      school__id=school_id)
    
    objs_to_create = []
    for term in terms:
        if Grade.objects.filter(school_term=term).exists():
            for enrollment in enrollments: 
                objs_to_create.append(
                    Grade(school_term=term, grade=None, subject=subject, 
                          enrollment=enrollment)
                )
    if objs_to_create:
        Grade.objects.bulk_create(objs_to_create)


@shared_task(bind=True)
def generate_fiche_table(self, user_id, year_id, education):
    recorder = ProgressRecorder(self)

    user = CustomUser.objects.get(id=user_id)
    year = Year.objects.get(id=year_id)
    file_type = PDFFile.CATEGORY_FICHE_TABLE
    levels = Level.objects.for_user(
        user=user, year=year, education=education,
        with_education=True, with_files=True,
        file_type=file_type)
    total = levels.count()

    for i, level in enumerate(levels):
        if not level.is_clean:
            queryset = Enrollment.objects.for_user(user=user, year=year)
            if education == EDUCATION_FRENCH:
                queryset = queryset.filter(level_fr=level)
            else:
                queryset = queryset.filter(level_ar=level)
            doc = reports.FicheTable()
            doc.add_content(queryset=queryset, level=level)
            
            media_path = os.path.join('/app/media/')
            if not os.path.exists(media_path):
                os.mkdir(media_path)
            
            pdf_path = os.path.join('/app/media/pdf/')
            if not os.path.exists(pdf_path):
                os.mkdir(pdf_path)

            filename = f"Fiches de table {year}" + str(level) + " " + \
                        str(level.get_education_display()) + \
                        f' [{user.school_id}]'
            filepath = os.path.join(pdf_path, f'{filename}.pdf')
            doc.output(filepath)
            PDFFile.objects.update_or_create(
                defaults={'path':filepath, 'is_clean':True}, 
                level=level, category=file_type
            )
        recorder.set_progress(i + 1, total, f'{round(i * 100 / total)} %')


@shared_task(bind=True)
def generate_reports(self, user_id, year_id, term_id, 
                     education, level_id=None, 
                     file_type=PDFFile.CATEGORY_REPORT):
    recorder = ProgressRecorder(self)

    user = CustomUser.objects.get(id=user_id)
    year = Year.objects.get(id=year_id)
    generic_term = Term.objects.get(pk=term_id)
    levels = Level.objects.for_user(
        user=user, year=year, education=education,
        with_education=True, with_files=True,
        file_type=file_type, term=generic_term)

    if level_id:
        levels = levels.filter(pk=level_id)
        
    if education == EDUCATION_FRENCH:
        levels = levels.annotate(
            students=Count('enrollment', distinct=True)
        )
    else:
        levels = levels.annotate(
            students=Count('enrollment_ar', distinct=True)
        )
    
    if generic_term.cycle != CYCLE_BOTH:
        levels = levels.filter(generic_level__cycle=generic_term.cycle)
    
    total = levels.count()
    print('Start - Generating reports task ....', 'term', generic_term, 'education', 
          education, 'year', year, 'levels', levels.count(), file_type)

    # Pull data from db
    for i, level in enumerate(levels):
        term = SchoolTerm.objects.get(
            term__id=term_id, year__id=year_id, 
            level__id=level.generic_level.id, 
            school__id=user.school_id, education=generic_term.education)
        
        if level.students > 0:
            # queryset = Enrollment.objects.for_user(user=user, year=year)
            term_is_first = views.is_first_term(user=user, level=level, term=term)
            term_is_last = views.is_last_term(user=user, level=level, term=term)
            
            # Skip reports if is first term for level
            if file_type == PDFFile.CATEGORY_REPORT_WITH_PREVIOUS_TERMS_DATA and term_is_first:
                continue

            queryset = views.annotate_enrollments_with_students_infos(
                user=user, year=year, term=term)
            
            queryset = views.annotate_enrollment_qs_with_term_grades_data(
                queryset=queryset, user=user, term=term, level=level,
                term_is_first=term_is_first, term_is_last=term_is_last,
                report_type=file_type)
            
            if education == EDUCATION_FRENCH:
                queryset = queryset.filter(level_fr=level)
                # queryset = queryset.order_by('student__last_name', 'student__first_name')
            else:
                queryset = queryset.filter(level_ar=level)
                queryset = queryset.order_by('student__full_name_ar')
            doc = None

            # Use appropriate report class and pass data to .add_content() method
            cycle = level.generic_level.cycle
            if education == EDUCATION_FRENCH and cycle == CYCLE_PRIMARY:
                doc = reports.PrimaryReportFr()
            elif education == EDUCATION_FRENCH and cycle == CYCLE_SECONDARY:
                doc = reports.SecondCycleReport()
            elif education == EDUCATION_ARABIC and cycle == CYCLE_PRIMARY:
                doc = reports.PrimaryReportAr()
            elif education == EDUCATION_ARABIC and cycle == CYCLE_SECONDARY:
                doc = reports.SecondCycleReportAr()

            if education == EDUCATION_FRENCH and cycle == CYCLE_SECONDARY:
                doc.add_content(queryset=queryset, term=term, year=year, annual_report=term_is_last)
            elif cycle == CYCLE_SECONDARY:
                doc.add_content(
                    user=user, queryset=queryset, term=term, 
                    level=level, is_last_term=term_is_last,
                    report_type=file_type)
            else:
                doc.add_content(
                    user=user, queryset=queryset, term=term, 
                    level=level, report_type=file_type,
                    is_last_term=term_is_last)
            
            # Select or create appropriate path and generate report
            media_path = os.path.join('/app/media')
            if not os.path.exists(media_path):
                os.mkdir(media_path)
            
            pdf_path = os.path.join('/app/media/pdf/')
            if not os.path.exists(pdf_path):
                os.mkdir(pdf_path)

            filename = ''
            if file_type == PDFFile.CATEGORY_REPORT:
                filename = f"Bulletins de notes {year} {level} {term} {level.get_education_display()} [{user.school_id}]"
            else:
                filename = f"Bulletins de notes {year} {level} {term} {level.get_education_display()} Modele 2 [{user.school_id}]"

            filepath = os.path.join(pdf_path, f'{filename}.pdf')
            doc.output(filepath)
            PDFFile.objects.update_or_create(
                defaults={'path':filepath, 'is_clean':True}, 
                level=level, category=file_type, term=generic_term
            )
        recorder.set_progress(i + 1, total, f'{round(i * 100 / total)} %')