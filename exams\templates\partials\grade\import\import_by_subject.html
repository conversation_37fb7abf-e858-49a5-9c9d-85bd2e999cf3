{% load widget_tweaks %}
{% load sweetify %} {% sweetify %}
<form class="tile" method="post" hx-post="{{ request.path }}?lang={{ lang }}" enctype="multipart/form-data"
    hx-on="htmx:afterRequest: $('#submit-btn').prop('disabled', false);">
    {% csrf_token %}

    <p style="font-size: 12pt;">
        Avant d'importer un fichier, veuillez vous assurer:
    </p>
    <ul style="list-style: decimal; font-size: 12pt;">
        <li>Qu'il est dans un format pris en charge <span class="font-weight-bold text-danger">Excel ou CSV</span></li>
        <li>
            Qu'il comporte une entête avec les champs obligatoires, à savoir: 
            <span class="text-danger font-weight-bold">matricule, les codes des matières à importer, le code_période et rang</span>
        </li>
    </ul>

    <h5>Veuillez sélectionner le fichier à importer:</h5>
    <div class="form-group">
        <input type="file" name="file" id="file" class='form-control' accept=".xlsx">
    </div>
    <input type="submit" value="Importer" class="btn btn-success" id="submit-btn">
</form>