{% csrf_token %}
{% if term %}
<select name="selected-term" id="selected-term" class="d-none my-0">
    <option value="{{ term }}">Période</option>
</select>
<!-- <div class="">
    <a href="" class="btn btn-warning" hx-get="{% url 'exams:generate_reports' %}?lang={{ lang }}"
        hx-target="#progress_container">Actualiser les bulletins</a>
</div> -->
<!-- <div id="progress_container" class="my-2"></div> -->

{% endif %}
<table class="table table-striped table-bordered table-sm table-hover" id="table">
    <thead class="bg-primary text-white">
    <tr>
        <!-- <th><input type="checkbox" name="select-all" id="select-all" onclick="checkAll(this)"></th> -->
        <th class="text-center">#</th>
        <th class="text-center">Classe</th>
        <th class="text-center" colspan="3">Effectif</th>
        <th class="text-center" colspan="3">Admis</th>
        <th class="text-center">Résultats</th>
    </tr>
    <tr class="text-center text-dark" style="background-color: lightcyan; font-weight: bold;">
        <th class="text-center"></th>
        <th class="text-center">Classe</th>
        <th class="text-center">G</th>
        <th class="text-center">F</th>
        <th class="text-center">T</th>
        <th class="text-center">G</th>
        <th class="text-center">F</th>
        <th class="text-center">T</th>
        <th class="text-center">Résultats annuels</th>
    </tr>
    </thead>
    <tbody>
    {% for level in queryset %}
    <tr>
        <td class="align-middle text-center">{{ forloop.counter }}</td>
        <td class="align-middle text-center">{{ level }}</td>
        <td class="align-middle text-center">{{ level.boys_count }}</td>
        <td class="align-middle text-center">{{ level.girls_count }}</td>
        <td class="align-middle text-center">{{ level.students }}</td>

        {% if level.boys_admitted %}
            <td class="align-middle text-center">{{ level.boys_admitted }}</td>
        {% else %}
            <td class="align-middle text-center">-</td>
        {% endif %}

        {% if level.girls_admitted %}
            <td class="align-middle text-center">{{ level.girls_admitted }}</td>
        {% else %}
            <td class="align-middle text-center">-</td>
        {% endif %}

        {% if level.students_admitted %}
            <td class="align-middle text-center">{{ level.girls_admitted }}</td>
        {% else %}
            <td class="align-middle text-center">-</td>
        {% endif %}

        <td class="align-middle">
            <div class="dropdown">
                <button class="btn btn-success btn-sm dropdown-toggle w-100" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span data-feather="chevrons-down" class="feather-16 align-middle"></span> {{ level }}
                </button>
                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    <a class="dropdown-item" href="{% url 'exams:annual_results' %}?level={{ level.id }}&ordre=merite">
                        <span data-feather="chevrons-up" class="feather-16 align-middle"></span> Ordre de mérite
                    </a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="{% url 'exams:annual_results' %}?level={{ level.id }}">
                        <span data-feather="chevrons-down" class="feather-16 align-middle"></span> Ordre alphabétique
                    </a>
                </div>
            </div>
        </td>
    </tr>
    {% endfor %}
    </tbody>
</table>

<script>
    $.fn.dataTable.ext.errMode = 'none';
		$('#table').DataTable({
		// dom: 'Bfrtip',
		lengthMenu: [
			[ 10, 25, 50, 100, 300],
			[ '10', '25', '50', '100', '300']],
        order: [],
        drawCallback: function() {
            htmx.process(document.body.querySelector('#table'))
            feather.replace();
        }
    })

    htmx.process(document.body)
    feather.replace();
</script>