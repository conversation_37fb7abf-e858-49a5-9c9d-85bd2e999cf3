{% load exams_tags %}

{% csrf_token %}
{% if term %}
<select name="selected-term" id="selected-term" class="d-none my-0">
    <option value="{{ term }}">Période</option>
</select>
{% endif %}
{% if request.GET.option == 'bulletins' %}
<div class="alert alert-info"><span data-feather="info" class="feather-16 align-middle"></span> Vous pouvez imprimer les bulletins par lot de 15 élèves.</div>
<div class="alert bg-light">
    <a href="#" onclick="event.preventDefault()" class="btn btn-sm btn-warning">Modèle 1: Par défaut</a>
    <a href="#" onclick="event.preventDefault()" class="btn btn-sm btn-info">Modèle 2: Avec Rappels</a>
</div>
{% endif %}
<table class="table table-striped table-bordered table-sm table-hover" id="table">
    <thead class="bg-primary text-white">
        <tr>
            <!-- <th><input type="checkbox" name="select-all" id="select-all" onclick="checkAll(this)"></th> -->
            <th class="text-center">#</th>
            <th class="text-center sticky-col sticky-header">Classe</th>
            {% if request.GET.option == 'resultats' %}
            <th class="text-center">Résultats Détaillés (PDF)</th>
            <th class="text-center">Résultats Résumé (PDF)</th>
            <th class="text-center" colspan="3">Effectifs</th>
            <th class="text-center">Notés</th>
            <th class="text-center" colspan="2">Garçons admis</th>
            <th class="text-center" colspan="2">Filles admises</th>
            <th class="text-center" colspan="2">Total</th>
            {% endif %}
            {% if request.GET.option == 'bulletins' %}
            <th class="text-center">Par lot</th>
            <th class="text-center">Par classe</th>
            {% endif %}
        </tr>
        <tr class="text-center text-dark" style="background-color: lightcyan; font-weight: bold;">
            <th class="text-center"></th>
            <th class="text-center sticky-col"></th>
            {% if request.GET.option == 'resultats' %}
            <th class="text-center"></th>
            <th class="text-center"></th>
            <th class="text-center">G</th>
            <th class="text-center">F</th>
            <th class="text-center">T</th>
            <th class="text-center"></th>
            <th class="text-center">Adm.</th>
            <th class="text-center">%</th>
            <th class="text-center">Adm.</th>
            <th class="text-center">%</th>
            <th class="text-center">Adm.</th>
            <th class="text-center">%</th>
            {% else %}
            <th></th>
            <th></th>
            {% endif %}

        </tr>
    </thead>
    <tbody>
    {% for level in queryset %}
    <tr>
        <!-- <td>
            <input type="checkbox" name="{{ level.id }}" id="{{ level.id }}" onclick="validateForm()">
        </td>  -->
        <td class="align-middle text-center">{{ forloop.counter }}</td>
        <td class="align-middle text-center sticky-col {% if not forloop.counter|divisibleby:'2' %} bg-lightgray {% else %} bg-white {% endif %}">{{ level }}</td>
        {% if request.GET.option == 'resultats' %}
        <td>
            <div class="dropdown">
                <button class="btn btn-success btn-sm dropdown-toggle w-100 {% if not level.term_id %} disabled {% endif %}" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span data-feather="chevrons-down" class="feather-16 align-middle"></span> {{ level }}
                </button>
                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    <a target="_blank" class="dropdown-item" href="{% url 'exams:period_results_pdf' %}?level={{ level.id }}&selected-term={{ level.term_id }}&generic_term={{ term }}&all">
                        <span data-feather="chevrons-up" class="feather-16 align-middle"></span> Ordre de mérite
                    </a>
                    <div class="dropdown-divider"></div>
                    <a target="_blank" class="dropdown-item" href="{% url 'exams:period_results_pdf' %}?level={{ level.id }}&selected-term={{ level.term_id }}&generic_term={{ term }}&ordering=alpha&all">
                        <span data-feather="chevrons-down" class="feather-16 align-middle"></span> Ordre alphabétique
                    </a>
                </div>
            </div>
        </td>
        <td>
            <div class="dropdown">
                <button class="btn btn-info btn-sm dropdown-toggle w-100 {% if not level.term_id %} disabled {% endif %}" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span data-feather="chevrons-down" class="feather-16 align-middle"></span> {{ level }}
                </button>
                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    <a target="_blank" class="dropdown-item" href="{% url 'exams:period_results_pdf' %}?level={{ level.id }}&selected-term={{ level.term_id }}&generic_term={{ term }}&all&summarized">
                        <span data-feather="chevrons-up" class="feather-16 align-middle"></span> Ordre de mérite
                    </a>
                    <div class="dropdown-divider"></div>
                    <a target="_blank" class="dropdown-item" href="{% url 'exams:period_results_pdf' %}?level={{ level.id }}&selected-term={{ level.term_id }}&generic_term={{ term }}&ordering=alpha&all&summarized">
                        <span data-feather="chevrons-down" class="feather-16 align-middle"></span> Ordre alphabétique
                    </a>
                </div>
            </div>        
        </td>
        {% endif %}
        {% if request.GET.option == 'resultats' %}
        <td class="align-middle text-center">{{ level.boys_count }}</td>
        <td class="align-middle text-center">{{ level.girls_count }}</td>
        <td class="align-middle text-center">{{ level.students }}</td>
        <td class="align-middle text-center">{% if level.students and level.marked %} {{ level.marked }} {% else %}-{% endif %}</td>
        <td class="align-middle text-center">{% if level.students and level.boys_admitted %} {{ level.boys_admitted }} {% else %} - {% endif %}</td>
        <td class="align-middle text-center">{% if level.boys_perc %} {{ level.boys_perc }} % {% else %} - {% endif %}</td>
        <td class="align-middle text-center">{% if level.students and level.girls_admitted %} {{ level.girls_admitted }} {% else %} - {% endif %}</td>
        <td class="align-middle text-center">{% if level.girls_perc %} {{ level.girls_perc }} % {% else %} - {% endif %}</td>
        <td class="align-middle text-center">{% if level.students and level.students_admitted %}{{ level.students_admitted }} {% else %} - {% endif %}</td>
        <td class="align-middle text-center">{% if level.students_perc %} {{ level.students_perc }} % {% else %} - {% endif %}</td>
        {% endif %}
        {% if request.GET.option == 'bulletins' %}
        <td>{% if level.range_of_15 %}
                {% for i in level.range_of_15|times %}
                <a class="btn btn-sm btn-warning" target="_blank" href="{% url 'exams:grade_action' %}?action=generate_report&level={{ level.id }}&lot={{ i|add:1 }}&all&generic_term={{ term }}&report_type=RP">Lot {{ i|add:1 }}</a>
                {% endfor %} <br>
                {% if not is_first_term %}
                    {% for i in level.range_of_15|times %}
                    <a class="btn btn-sm btn-info mt-1" target="_blank" href="{% url 'exams:grade_action' %}?action=generate_report&level={{ level.id }}&lot={{ i|add:1 }}&all&generic_term={{ term }}&report_type=RW">Lot {{ i|add:1 }}</a>
                    {% endfor %}
                {% endif %}
            {% else %} - {% endif %}
        </td>
        <td>
            {% if level.is_clean and level.file_id or level.file_id2 %}
                {% if level.file_id %}
                    <a class="btn btn-sm btn-warning" href="{% url 'exams:file_download' level.file_id %}">
                        <span data-feather="file-text" class="feather-16 align-middle"></span>
                        Modèle 1
                    </a>
                {% endif %}

                {% if level.file_id2 %}
                    <a class="btn btn-sm btn-info" href="{% url 'exams:file_download' level.file_id2 %}">
                        <span data-feather="file-text" class="feather-16 align-middle"></span>
                        Modèle 2
                    </a>
                {% endif %}
            {% else %}
                <a href="#" class="btn btn-sm btn-danger w-100 disabled">
                    <span data-feather="file-text" class="align-middle text-center feather-16"></span>Actualiser
                </a>
            {% endif %}
        </td>
        {% endif %}
    </tr>
    {% endfor %}
    </tbody>
</table>

<div class="alert alert-warning mt-2">
    <span data-feather="info" class="feather-16 align-middle"></span> Si vous souhaitez générer tous les bulletins à la fois pour pouvoir télécharger par classe, veuillez 
    cliquer sur Actualiser, puis patienter. Cela peut prendre du temps.
</div>
<div class="">
    <a href="" class="btn btn-warning" hx-get="{% url 'exams:generate_reports' %}?lang={{ lang }}"
        hx-target="#progress_container" id="submit-btn">Actualiser les bulletins</a>
</div>
<div id="progress_container" class="my-2"></div>


<script>
    function checkAll(checkbox) {
      var checkboxes = document.getElementsByTagName('input');
      for (var i = 0; i < checkboxes.length; i++) {
        if (checkboxes[i].type === 'checkbox') {
          checkboxes[i].checked = checkbox.checked;
        }
      }

    //   validateForm()
    }

    // function validateForm(event) {
    //         console.log('Validating');
    // // Get all checkboxes within the form
    //         var checkboxes = document.querySelectorAll('input[type="checkbox"]');

    //         // Check if any of the checkboxes are checked
    //         var isChecked = Array.from(checkboxes).some(function(checkbox) {
    //             return checkbox.checked;
    //         });

    //         // If none of the checkboxes are checked, prevent form submission
    //         if (!isChecked) {
    //             if (!document.querySelector('#submit-btn').classList.contains('disabled')) {
    //                 document.querySelector('#submit-btn').classList.add('disabled')
    //             };
    //             document.querySelector('#submit-btn').style.pointerEvents = "none"
    //             return false
    //         }

    //         // If at least one checkbox is checked, allow the form to be submitted
    //         if (document.querySelector('#submit-btn').classList.contains('disabled')) {
    //                 document.querySelector('#submit-btn').classList.remove('disabled')
    //         };
    //         document.querySelector('#submit-btn').style.pointerEvents = "auto";


    // }

    $.fn.dataTable.ext.errMode = 'none';
		$('#table').DataTable({
		// dom: 'Bfrtip',
		lengthMenu: [
        [ 10, 25, 50, 100, 300],
        [ '10', '25', '50', '100', '300']],
        order: [],
        drawCallback: function() {
            htmx.process(document.body.querySelector('#table'))
            feather.replace();
        }
    })

    htmx.process(document.body)
    feather.replace();
</script>