from django.shortcuts import render
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Sum
from .models import Student, Enrollment, Teacher, Payment
from school.views import get_session_year

class MaterialDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'material/dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        year = get_session_year(self.request)
        
        # Get statistics
        context['total_students'] = Enrollment.objects.filter(
            school=user.school, 
            year=year, 
            active=True
        ).count()
        
        context['total_teachers'] = Teacher.objects.filter(
            school=user.school,
            active=True
        ).count()
        
        context['total_classes'] = Enrollment.objects.filter(
            school=user.school,
            year=year
        ).values('level_fr').distinct().count()
        
        # Get recent payments
        context['recent_payments'] = Payment.objects.filter(
            enrollment__school=user.school
        ).order_by('-created_at')[:10]
        
        # Calculate total payments for the current month
        from datetime import datetime
        current_month = datetime.now().month
        current_year = datetime.now().year
        
        total_payments = Payment.objects.filter(
            enrollment__school=user.school,
            created_at__month=current_month,
            created_at__year=current_year
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        context['total_payments'] = total_payments
        
        # Mock recent activities (in a real app, this would come from a model)
        context['recent_activities'] = [
            {
                'icon': 'person_add',
                'title': 'Nouvel élève inscrit',
                'description': 'John Doe a été inscrit en classe de 6ème',
                'timestamp': datetime.now()
            },
            {
                'icon': 'payment',
                'title': 'Paiement reçu',
                'description': 'Paiement de 50 000 FCFA reçu pour Jane Smith',
                'timestamp': datetime.now()
            },
            {
                'icon': 'school',
                'title': 'Nouvelle classe créée',
                'description': 'La classe de 3ème B a été créée',
                'timestamp': datetime.now()
            }
        ]
        
        return context
