/* Login Page Styles */
.login-body {
    background-color: var(--md-background);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 24px;
}

.login-container {
    width: 100%;
    max-width: 450px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.login-card {
    width: 100%;
    border-radius: 8px;
    padding: 40px;
    box-shadow: var(--md-elevation-level1);
    background-color: var(--md-surface);
}

.login-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 32px;
}

.login-logo {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background-color: var(--md-primary-container);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
}

.login-logo .material-icons {
    font-size: 36px;
    color: var(--md-on-primary-container);
}

.login-title {
    font-size: 24px;
    font-weight: 500;
    color: var(--md-on-surface);
    margin: 0 0 8px 0;
    text-align: center;
}

.login-subtitle {
    font-size: 16px;
    font-weight: 400;
    color: var(--md-on-surface-variant);
    margin: 0;
    text-align: center;
}

.login-form {
    width: 100%;
}

.login-field {
    width: 100%;
    margin-bottom: 24px;
    height: 56px;
    position: relative;
}

/* Ensure proper spacing for the text field content */
.login-field.mdc-text-field--filled:not(.mdc-text-field--disabled) {
    background-color: var(--md-surface);
}

/* Adjust input padding to prevent overlap with icons */
.login-field .mdc-text-field__input {
    padding-left: 10px !important; /* Ensure space for the leading icon */
}

/* Style select dropdown */
.login-field select.mdc-text-field__input {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23757575' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px !important;
}


.login-field .mdc-text-field__icon,
.mdc-select--filled.mdc-field--with-leading-icon {
    color: var(--md-on-surface-variant);
    z-index: 2;
    position: relative;
    margin-left: 0px;
    background-color: var(--md-surface);
}


.login-field.mdc-text-field--focused .mdc-text-field__icon,
.login-field.mdc-text-field:hover .mdc-text-field__icon {
    background-color: transparent;
}



.mdc-select--filled.mdc-field--with-leading-icon .mdc-floating-label {
    color: var(--md-on-surface-variant);
    /* margin-left: 10px !important; Add space to prevent overlap with the icon */
    background-color: transparent;
    z-index: 1;
}

.mdc-select--filled.mdc-field--with-leading-icon .mdc-select__selected-text {
    padding-left: 7px;
}

.login-field .mdc-floating-label {
    color: var(--md-on-surface-variant);
    margin-left: 30px !important; /* Add space to prevent overlap with the icon */
    background-color: transparent;
    z-index: 1;
}

/* Adjust the position of the floating label when the field is focused or filled */
.login-field.mdc-text-field--focused .mdc-floating-label,
.login-field.mdc-text-field--label-floating .mdc-floating-label,
.mdc-select--filled .mdc-floating-label {
    margin-left: 0 !important;
    color: var(--md-on-surface);
    position: absolute;
    /* top: 20px */
    left: 47px
}

.mdc-field--with-leading-icon .mdc-text-field__input {
    /* position: absolute; */
    /* left: 48px */
    padding-left: 0px !important;
}




/* Removed duplicate rule */

.login-field .mdc-line-ripple::before {
    border-bottom-color: var(--md-outline);
}

.login-field .mdc-line-ripple::after {
    border-bottom-color: var(--md-primary);
}

.password-toggle {
    cursor: pointer;
    z-index: 2;
    position: relative;
    background-color: transparent !important;
}

.login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
}

.mdc-checkbox {
    --mdc-theme-secondary: var(--md-primary);
}

.forgot-password {
    color: var(--md-primary);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: opacity 0.2s ease;
}

.forgot-password:hover {
    opacity: 0.8;
}

.login-button {
    width: 100%;
    height: 40px;
    margin-bottom: 24px;
    --mdc-theme-primary: var(--md-primary);
    --mdc-theme-on-primary: var(--md-on-primary);
}

.create-account {
    text-align: center;
    font-size: 14px;
    color: var(--md-on-surface-variant);
}

.create-account-link {
    color: var(--md-primary);
    text-decoration: none;
    font-weight: 500;
    margin-left: 4px;
    transition: opacity 0.2s ease;
}

.create-account-link:hover {
    opacity: 0.8;
}

.login-footer {
    margin-top: 40px;
    text-align: center;
}

.footer-links {
    margin-bottom: 8px;
}

.footer-links a {
    color: var(--md-on-surface-variant);
    text-decoration: none;
    font-size: 12px;
    transition: color 0.2s ease;
}

.footer-links a:hover {
    color: var(--md-on-surface);
}

.footer-divider {
    margin: 0 8px;
    color: var(--md-outline);
    font-size: 12px;
}

.footer-copyright {
    color: var(--md-on-surface-variant);
    font-size: 12px;
    margin-bottom: 8px;
}

.footer-contact {
    color: var(--md-on-surface-variant);
    font-size: 12px;
    margin-bottom: 8px;
}

.install-app {
    margin-top: 16px;
}

.login-error {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background-color: rgba(var(--md-error-rgb), 0.1);
    border-radius: 4px;
    color: var(--md-error);
    margin-bottom: 16px;
    animation: fadeInUp 0.5s ease forwards;
    opacity: 0;
    animation-delay: 0.4s;
}

.login-error .material-icons {
    font-size: 20px;
}

/* Animation for form elements */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-form {
    animation: fadeInUp 0.2s ease forwards;
    opacity: 0;
}

.login-logo {
    animation-delay: 0.1s;
}

.login-title {
    animation-delay: 0.2s;
}

.login-subtitle {
    animation-delay: 0.3s;
}

#year-field {
    animation-delay: 0.4s;
}

#username-field {
    animation-delay: 0.5s;
}

#password-field {
    animation-delay: 0.6s;
}

.login-options {
    animation-delay: 0.7s;
}

.login-button {
    animation-delay: 0.8s;
}

.create-account {
    animation-delay: 0.9s;
}

/* Responsive adjustments */
@media (max-width: 600px) {
    .login-card {
        padding: 24px;
    }

    .login-logo {
        width: 56px;
        height: 56px;
    }

    .login-logo .material-icons {
        font-size: 32px;
    }

    .login-title {
        font-size: 20px;
    }

    .login-subtitle {
        font-size: 14px;
    }

    .login-options {
        flex-direction: column;
        align-items: flex-start;
    }

    .forgot-password {
        margin-top: 16px;
    }
}
