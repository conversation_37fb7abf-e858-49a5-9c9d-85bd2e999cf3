document.addEventListener('DOMContentLoaded', () => {
  const rowCheckboxes = Array.from(document.querySelectorAll('.mdc-data-table__row-checkbox .mdc-checkbox__native-control') || []);
  const headerCheckbox = document.querySelector('.mdc-data-table__header-row-checkbox .mdc-checkbox__native-control');
  const bottomActionsBar = document.getElementById('bottom-actions-bar');
  const actionsSelectElement = document.getElementById('actions-select');

  let actionsSelect;
  if (actionsSelectElement) {
    actionsSelect = new mdc.select.MDCSelect(actionsSelectElement);
  }

  const goButton = document.getElementById('action-go');
  const pinButton = document.getElementById('action-pin');
  const archiveButton = document.getElementById('action-archive');
  const deleteButton = document.getElementById('action-delete');

  function updateBottomBar() {
    if (bottomActionsBar && rowCheckboxes.length > 0) {
      const anyChecked = rowCheckboxes.some(cb => cb.checked);
      bottomActionsBar.classList.toggle('show', anyChecked);
    }
  }

  function getSelectedRows() {
    return rowCheckboxes
      .map((cb, idx) => cb.checked ? idx : -1)
      .filter(idx => idx > -1);
  }

  function doBulkAction(action) {
    const rows = getSelectedRows();
    console.log(`Bulk ${action} on rows:`, rows);
    // TODO: implement action logic here
    rowCheckboxes.forEach(cb => cb.checked = false);
    if (headerCheckbox) headerCheckbox.checked = false;
    updateBottomBar();
  }

  if (rowCheckboxes.length > 0) {
    rowCheckboxes.forEach(cb => cb.addEventListener('change', updateBottomBar));
  }

  if (headerCheckbox) {
    headerCheckbox.addEventListener('change', () => {
      rowCheckboxes.forEach(cb => cb.checked = headerCheckbox.checked);
      updateBottomBar();
    });
  }

  if (goButton && actionsSelect) {
    goButton.addEventListener('click', () => {
      const action = actionsSelect.value;
      if (action) doBulkAction(action);
    });
  }

  if (pinButton) pinButton.addEventListener('click', () => doBulkAction('pin'));
  if (archiveButton) archiveButton.addEventListener('click', () => doBulkAction('archive'));
  if (deleteButton) deleteButton.addEventListener('click', () => doBulkAction('delete'));

  const closeActionsBtn = document.getElementById('close-actions');
  if (closeActionsBtn && bottomActionsBar) {
    closeActionsBtn.addEventListener('click', () => {
      bottomActionsBar.classList.remove('show');
      if (actionsSelect) {
        actionsSelect.selectedIndex = 0; // reset select
      }
    });
  }
});