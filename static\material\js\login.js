document.addEventListener('DOMContentLoaded', function() {
    // Hide preloader when page is loaded
    const preloader = document.querySelector('.preloader');
    if (preloader) {
        setTimeout(function() {
            preloader.style.display = 'none';
        }, 500);
    }

    // Initialize text fields
    const textFields = document.querySelectorAll('.mdc-text-field');
    textFields.forEach(textField => {
        mdc.textField.MDCTextField.attachTo(textField);
    });

    // Initialize checkbox
    const checkbox = document.querySelector('.mdc-checkbox');
    if (checkbox) {
        mdc.checkbox.MDCCheckbox.attachTo(checkbox);
    }

    // Initialize button
    const button = document.querySelector('.mdc-button');
    if (button) {
        mdc.ripple.MDCRipple.attachTo(button);
    }

    // Toggle password visibility
    const passwordToggle = document.querySelector('.password-toggle');
    const passwordInput = document.getElementById('password');

    if (passwordToggle && passwordInput) {
        passwordToggle.addEventListener('click', function() {
            const isPassword = passwordInput.type === 'password';
            passwordInput.type = isPassword ? 'text' : 'password';
            passwordToggle.textContent = isPassword ? 'visibility' : 'visibility_off';
        });
    }

    // Handle login form submission
    const loginButton = document.getElementById('login-button');

    if (loginButton) {
        loginButton.addEventListener('click', function(e) {
            e.preventDefault();

            // Show brief loading state
            loginButton.disabled = true;
            loginButton.querySelector('.mdc-button__label').textContent = 'Signing in...';

            // Immediately redirect to dashboard
            window.location.href = 'index.html';
        });
    }
});
