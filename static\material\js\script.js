// Preloader functionality
window.addEventListener('load', function() {
    // Hide preloader when page is fully loaded
    const preloader = document.querySelector('.preloader');
    preloader.classList.add('hidden');

    // Remove preloader from DOM after transition completes
    preloader.addEventListener('transitionend', function() {
        preloader.style.display = 'none';
    });
});

// Initialize Material Components
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all Material Design components
    mdc.autoInit();

    // HTMX Progress Bar

    // Show progress bar when HTMX request starts
    document.body.addEventListener('htmx:beforeRequest', function() {
        document.body.classList.add('htmx-request');
    });

    // Hide progress bar when HTMX request completes
    document.body.addEventListener('htmx:afterRequest', function() {
        document.body.classList.remove('htmx-request');
    });

    // Hide progress bar when HTMX request encounters an error
    document.body.addEventListener('htmx:responseError', function() {
        document.body.classList.remove('htmx-request');
    });

    // Initialize ripple effect for all buttons with ripple surface
    const iconButtons = document.querySelectorAll('.mdc-icon-button');
    iconButtons.forEach(button => {
        const ripple = new mdc.ripple.MDCRipple(button);
        ripple.unbounded = true;
    });

    // Initialize top app bar
    mdc.topAppBar.MDCTopAppBar.attachTo(document.querySelector('.mdc-top-app-bar'));

    // Get sidebar elements
    const sidebar = document.getElementById('sidebar');
    const sidebarBackdrop = document.getElementById('sidebar-backdrop');

    // Custom user drawer variables
    const userDrawerContainer = document.getElementById('user-drawer-container');
    const userDrawerBackdrop = document.getElementById('user-drawer-backdrop');

    // Initialize notifications menu
    const notificationsMenu = new mdc.menu.MDCMenu(document.querySelector('#notifications-menu'));
    notificationsMenu.setAnchorCorner(mdc.menu.Corner.TOP_START);

    // Add ripple effect to all list items
    document.querySelectorAll('.mdc-list-item').forEach(item => {
        mdc.ripple.MDCRipple.attachTo(item);
    });

    // Add ripple effect to all buttons
    document.querySelectorAll('.mdc-button').forEach(button => {
        mdc.ripple.MDCRipple.attachTo(button);
    });

    // Get dashboard content element
    const dashboardContent = document.querySelector('.dashboard-content');
    const menuButton = document.getElementById('menu-button');

    // Function to open sidebar
    function openSidebar() {
        sidebar.classList.add('open');
        sidebar.classList.remove('closed');

        // Only remove full-width class on larger screens
        if (window.innerWidth > 960) {
            dashboardContent.classList.remove('full-width');
        }

        console.log('Sidebar opened');
    }

    // Function to close sidebar
    function closeSidebar() {
        sidebar.classList.remove('open');
        sidebar.classList.add('closed');
        dashboardContent.classList.add('full-width');
        console.log('Sidebar closed');
    }

    // Toggle sidebar when menu button is clicked
    menuButton.addEventListener('click', function(event) {
        event.stopPropagation();

        if (sidebar.classList.contains('open')) {
            closeSidebar();
        } else {
            openSidebar();
        }
    });

    // Close sidebar when backdrop is clicked
    sidebarBackdrop.addEventListener('click', function() {
        closeSidebar();
    });

    // Close sidebar when clicking outside (for mobile)
    document.addEventListener('click', function(event) {
        if (sidebar.classList.contains('open') &&
            window.innerWidth <= 960 &&
            !event.target.closest('.sidebar') &&
            !event.target.closest('#menu-button')) {
            closeSidebar();
        }
    });

    // Handle responsive design
    function handleResize() {
        if (window.innerWidth <= 960) {
            closeSidebar();
        } else {
            openSidebar();
        }
    }

    // Initial call and event listener for resize
    handleResize();
    window.addEventListener('resize', handleResize);

    // Initialize data table if present
    const dataTableElement = document.querySelector('.mdc-data-table');
    if (dataTableElement) {
        // Initialize the data table
        new mdc.dataTable.MDCDataTable(dataTableElement);

        // Initialize checkboxes in the data table
        const checkboxes = document.querySelectorAll('.mdc-checkbox');
        const checkboxInstances = [];
        checkboxes.forEach(checkbox => {
            checkboxInstances.push(new mdc.checkbox.MDCCheckbox(checkbox));
        });

        // Add functionality to the "Select All" checkbox
        const headerCheckbox = document.querySelector('.mdc-data-table__header-row-checkbox');
        const rowCheckboxes = document.querySelectorAll('.mdc-data-table__row-checkbox');

        if (headerCheckbox) {
            headerCheckbox.addEventListener('click', function() {
                const isChecked = headerCheckbox.querySelector('.mdc-checkbox__native-control').checked;

                // Update all row checkboxes to match the header checkbox
                rowCheckboxes.forEach(checkbox => {
                    checkbox.querySelector('.mdc-checkbox__native-control').checked = isChecked;
                });

                // Log the action (in a real app, this would perform an action)
                console.log(isChecked ? 'Selected all rows' : 'Deselected all rows');
            });
        }

        // Add click handler for individual row checkboxes
        rowCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('click', function(e) {
                // Stop propagation to prevent row click handler from firing
                e.stopPropagation();

                // Check if all row checkboxes are checked
                const allChecked = Array.from(rowCheckboxes).every(cb =>
                    cb.querySelector('.mdc-checkbox__native-control').checked
                );

                // Update header checkbox accordingly
                if (headerCheckbox) {
                    headerCheckbox.querySelector('.mdc-checkbox__native-control').checked = allChecked;
                }

                // Log the action (in a real app, this would perform an action)
                const rowIndex = this.closest('tr').rowIndex - 1; // -1 to account for header
                console.log(`Row ${rowIndex} ${this.querySelector('.mdc-checkbox__native-control').checked ? 'selected' : 'deselected'}`);
            });
        });

        // Initialize the search field with reactive search functionality
        const searchField = document.querySelector('.search-field');
        if (searchField) {
            const textField = new mdc.textField.MDCTextField(searchField);
            const searchInput = document.getElementById('data-table-search');

            // Ensure the label floats if there's content in the input
            if ((searchInput) && (searchInput.value)) {
                searchField.classList.add('mdc-text-field--label-floating');
            }

            if (searchInput) {
                // Add debounce function to avoid excessive filtering
                let searchTimeout;

                // Function to filter table rows based on search input
                const filterTableRows = () => {
                    const searchTerm = searchInput.value.toLowerCase().trim();
                    const tableRows = document.querySelectorAll('.mdc-data-table__row');
                    const noResultsMessage = document.querySelector('.no-results-message');
                    let hasVisibleRows = false;

                    // Remove existing no results message if it exists
                    if (noResultsMessage) {
                        noResultsMessage.remove();
                    }

                    // Filter rows based on search term
                    tableRows.forEach(row => {
                        // Remove any existing highlights
                        row.querySelectorAll('.search-highlight').forEach(highlight => {
                            const parent = highlight.parentNode;
                            parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
                            // Normalize to merge adjacent text nodes
                            parent.normalize();
                        });

                        const rowText = row.textContent.toLowerCase();
                        const shouldShow = searchTerm === '' || rowText.includes(searchTerm);

                        row.style.display = shouldShow ? '' : 'none';
                        if (shouldShow && searchTerm !== '') {
                            hasVisibleRows = true;

                            // Highlight matching text
                            const cells = row.querySelectorAll('.mdc-data-table__cell:not(.mdc-data-table__cell--checkbox)');
                            cells.forEach(cell => {
                                // Skip cells with complex content (like those with severity spans)
                                if (cell.querySelector('span')) return;

                                const cellText = cell.textContent;
                                const lowerCellText = cellText.toLowerCase();
                                if (lowerCellText.includes(searchTerm)) {
                                    // Create a temporary container
                                    const tempContainer = document.createElement('div');
                                    tempContainer.innerHTML = cellText.replace(
                                        new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi'),
                                        match => `<span class="search-highlight">${match}</span>`
                                    );

                                    // Clear the cell and append the highlighted content
                                    cell.textContent = '';
                                    while (tempContainer.firstChild) {
                                        cell.appendChild(tempContainer.firstChild);
                                    }
                                }
                            });
                        }
                    });

                    // Show no results message if no rows match
                    if (!hasVisibleRows && searchTerm !== '') {
                        const tableBody = document.querySelector('.mdc-data-table__content');
                        const noResultsRow = document.createElement('tr');
                        noResultsRow.className = 'no-results-message';
                        noResultsRow.innerHTML = `
                            <td colspan="7" style="text-align: center; padding: 24px;">
                                <div style="display: flex; flex-direction: column; align-items: center; gap: 8px;">
                                    <span class="material-icons" style="font-size: 48px; color: var(--md-outline-variant);">search_off</span>
                                    <p>No results found for "${searchTerm}"</p>
                                </div>
                            </td>
                        `;
                        tableBody.appendChild(noResultsRow);
                    }

                    // Update pagination text
                    const visibleRowCount = Array.from(tableRows).filter(row => row.style.display !== 'none').length;
                    const paginationTotal = document.querySelector('.mdc-data-table__pagination-total');
                    if (paginationTotal) {
                        if (searchTerm === '') {
                            paginationTotal.textContent = '1-4 of 100';
                        } else {
                            paginationTotal.textContent = visibleRowCount > 0 ? `1-${visibleRowCount} of ${visibleRowCount}` : '0-0 of 0';
                        }
                    }
                };

                // Add input event listener with debounce
                searchInput.addEventListener('input', () => {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(filterTableRows, 300); // 300ms debounce
                });

                // Add clear button functionality
                const clearSearch = () => {
                    searchInput.value = '';
                    textField.value = '';
                    // Make sure the label is not floating when cleared
                    searchField.classList.remove('mdc-text-field--label-floating');
                    filterTableRows();
                    searchInput.focus();
                };

                // Clear search when Escape key is pressed
                searchInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        clearSearch();
                        e.preventDefault();
                    }
                });

                // Add clear button to search field if it has content
                searchInput.addEventListener('input', () => {
                    let clearButton = searchField.querySelector('.search-clear-button');

                    if (searchInput.value && !clearButton) {
                        clearButton = document.createElement('button');
                        clearButton.className = 'material-icons mdc-icon-button search-clear-button';
                        clearButton.innerHTML = '<div class="mdc-icon-button__ripple"></div>close';
                        clearButton.setAttribute('aria-label', 'Clear search');
                        searchField.appendChild(clearButton);

                        clearButton.addEventListener('click', clearSearch);
                    } else if (!searchInput.value && clearButton) {
                        clearButton.remove();
                    }
                });
            }
        }

        // Initialize the rows per page select
        const rowsPerPageSelect = document.querySelector('.mdc-data-table__pagination-rows-per-page-select');
        if (rowsPerPageSelect) {
            new mdc.select.MDCSelect(rowsPerPageSelect);
        }

        // Initialize filter chips
        const chipSet = document.querySelector('.mdc-chip-set');
        if (chipSet) {
            new mdc.chips.MDCChipSet(chipSet);
        }

        // Add event listeners for pagination buttons (UI only, no actual functionality)
        const paginationButtons = document.querySelectorAll('.mdc-data-table__pagination-button');
        paginationButtons.forEach(button => {
            button.addEventListener('click', function() {
                // This would normally handle pagination, but we're just implementing the UI
                console.log('Pagination button clicked:', this.getAttribute('data-page'));
            });
        });

        // Add row click handler to toggle checkbox
        const tableRows = document.querySelectorAll('.mdc-data-table__row');
        tableRows.forEach(row => {
            row.addEventListener('click', function(e) {
                // Don't toggle if clicking on a checkbox or action button
                if (e.target.closest('.mdc-checkbox') || e.target.closest('button')) {
                    return;
                }

                // Find the checkbox in this row and toggle it
                const checkbox = this.querySelector('.mdc-checkbox__native-control');
                if (checkbox) {
                    checkbox.checked = !checkbox.checked;

                    // Trigger a click event on the checkbox to update the header checkbox
                    const checkboxEvent = new MouseEvent('click', {
                        bubbles: true,
                        cancelable: true,
                        view: window
                    });
                    checkbox.dispatchEvent(checkboxEvent);
                }
            });
        });

        // Add event listeners for sort buttons (UI only, no actual functionality)
        const sortButtons = document.querySelectorAll('.mdc-data-table__sort-icon-button');
        sortButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Get the header cell
                const headerCell = this.closest('.mdc-data-table__header-cell');

                // Get the current sort state
                const currentSort = headerCell.getAttribute('aria-sort');

                // Reset all header cells
                document.querySelectorAll('.mdc-data-table__header-cell').forEach(cell => {
                    cell.setAttribute('aria-sort', 'none');
                });

                // Set the new sort state
                if (currentSort === 'none' || currentSort === 'descending') {
                    headerCell.setAttribute('aria-sort', 'ascending');
                } else {
                    headerCell.setAttribute('aria-sort', 'descending');
                }

                // This would normally trigger a sort, but we're just implementing the UI
                console.log('Sort button clicked:', headerCell.textContent.trim(), headerCell.getAttribute('aria-sort'));
            });
        });
    }

    // Add ripple effect to stats cards
    document.querySelectorAll('.stats-card').forEach(card => {
        // Initialize MDC Ripple for the card
        mdc.ripple.MDCRipple.attachTo(card);

        // Make the ripple properly respond to pointer events
        card.addEventListener('pointerdown', function(e) {
            const rect = card.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // Create a ripple effect at the pointer position
            const rippleDiv = document.createElement('div');
            rippleDiv.classList.add('stats-card-ripple');
            rippleDiv.style.left = `${x}px`;
            rippleDiv.style.top = `${y}px`;
            card.appendChild(rippleDiv);

            // Remove the ripple after animation completes
            setTimeout(() => {
                rippleDiv.remove();
            }, 800); // Match with the CSS animation duration
        });
    });

    // Handle user icon click to toggle user drawer
    const userButton = document.querySelector('.mdc-top-app-bar__action-item[aria-label="User"]');
    userButton.addEventListener('click', function() {
        userDrawerContainer.classList.add('open');
    });

    // Close user drawer when clicking on backdrop
    userDrawerBackdrop.addEventListener('click', function() {
        closeUserDrawer();
    });

    // Initialize search field
    const searchField = document.querySelector('.search-field');
    if (searchField) {
        const textField = new mdc.textField.MDCTextField(searchField);

        // Add focus to search field when clicked anywhere in the container
        searchField.addEventListener('click', function(event) {
            // Only focus if the click wasn't on the input itself (to avoid double focus)
            if (!event.target.classList.contains('mdc-text-field__input')) {
                textField.focus();
            }
        });

        // Handle Enter key press in search field
        const searchInput = searchField.querySelector('.mdc-text-field__input');
        searchInput.addEventListener('keydown', function(event) {
            if (event.key === 'Enter') {
                // Perform search action
                console.log('Search for:', searchInput.value);
                // You would typically call your search function here
            }
        });
    }

    // Mobile search functionality
    const mobileSearchButton = document.getElementById('mobile-search-button');
    const mobileSearchContainer = document.getElementById('mobile-search-container');
    const mobileSearchClose = document.getElementById('mobile-search-close');
    const mobileSearchInput = document.querySelector('.mobile-search-input');

    // Open mobile search
    mobileSearchButton.addEventListener('click', function(event) {
        event.stopPropagation();
        mobileSearchContainer.classList.add('open');
        // Focus the search input after a short delay to ensure the animation completes
        setTimeout(() => {
            mobileSearchInput.focus();
        }, 300);
    });

    // Close mobile search
    mobileSearchClose.addEventListener('click', function() {
        mobileSearchContainer.classList.remove('open');
    });

    // Close mobile search when pressing Escape key
    mobileSearchInput.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            mobileSearchContainer.classList.remove('open');
        }
    });

    // Handle notifications icon click to toggle notifications menu
    const notificationsButton = document.querySelector('.mdc-top-app-bar__action-item[aria-label="Notifications"]');
    notificationsButton.addEventListener('click', function(event) {
        event.stopPropagation();
        notificationsMenu.open = !notificationsMenu.open;

        // Position the menu correctly
        const rect = notificationsButton.getBoundingClientRect();
        document.querySelector('.notifications-anchor').style.top = rect.bottom + 'px';
        document.querySelector('.notifications-anchor').style.right = (window.innerWidth - rect.right) + 'px';
    });

    // Close notifications menu when clicking outside
    document.addEventListener('click', function(event) {
        // Close notifications menu when clicking outside
        if (notificationsMenu.open && !event.target.closest('#notifications-menu') &&
            !event.target.closest('.mdc-top-app-bar__action-item[aria-label="Notifications"]')) {
            notificationsMenu.open = false;
        }
    });

    // Add event listener to the close drawer button
    document.getElementById('close-user-drawer').addEventListener('click', function(event) {
        event.stopPropagation();
        closeUserDrawer();
    });

    // Function to close user drawer with smooth transition
    function closeUserDrawer() {
        const drawer = document.getElementById('user-drawer');
        const backdrop = document.getElementById('user-drawer-backdrop');

        // First animate the drawer and backdrop
        drawer.style.transform = 'translateX(100%)';
        backdrop.style.opacity = '0';

        // After the animation completes, hide the container
        setTimeout(function() {
            userDrawerContainer.classList.remove('open');
            // Reset inline styles to let CSS handle future animations
            drawer.style.transform = '';
            backdrop.style.opacity = '';
        }, 300); // Match this with the CSS transition duration
    }

    // Handle mark all as read button
    document.getElementById('mark-all-read').addEventListener('click', function() {
        document.querySelectorAll('.notification-item.unread').forEach(item => {
            item.classList.remove('unread');
        });
    });

    // Handle dark mode toggle
    const themeToggleButton = document.getElementById('theme-toggle-button');
    const prefersDarkScheme = window.matchMedia('(prefers-color-scheme: dark)');

    // Function to toggle dark mode
    function toggleDarkMode() {
        const isDarkMode = document.body.classList.toggle('dark-mode');

        // Update the icon based on the current mode
        if (isDarkMode) {
            themeToggleButton.textContent = 'dark_mode';
            localStorage.setItem('darkMode', 'enabled');
        } else {
            themeToggleButton.textContent = 'light_mode';
            localStorage.setItem('darkMode', 'disabled');
        }
    }

    // Check for saved user preference
    const savedDarkMode = localStorage.getItem('darkMode');

    // If the user has explicitly chosen a preference
    if (savedDarkMode === 'enabled') {
        document.body.classList.add('dark-mode');
        themeToggleButton.textContent = 'dark_mode';
    } else if (savedDarkMode === 'disabled') {
        document.body.classList.remove('dark-mode');
        themeToggleButton.textContent = 'light_mode';
    } else {
        // If no preference is saved, use system preference
        if (prefersDarkScheme.matches) {
            document.body.classList.add('dark-mode');
            themeToggleButton.textContent = 'dark_mode';
        }
    }

    // Add click event listener to the theme toggle button
    themeToggleButton.addEventListener('click', toggleDarkMode);

    // Using custom CSS tooltips with data-tooltip attribute
    // No JavaScript initialization needed

    // Initialize collapsible list items in sidebar
    const collapsibleItems = document.querySelectorAll('.mdc-list-item--collapsible');
    collapsibleItems.forEach(item => {
        item.addEventListener('click', function() {
            // Toggle expanded class
            this.classList.toggle('expanded');

            // Find the next sibling which should be the list group
            const listGroup = this.nextElementSibling;
            if (listGroup && listGroup.classList.contains('mdc-list-group')) {
                listGroup.classList.toggle('mdc-list-group--hidden');
            }
        });
    });

    // Handle bottom navigation bar interactions with custom ripple effects
    const bottomNavItems = document.querySelectorAll('.bottom-nav-item');
    bottomNavItems.forEach(item => {
        // Add click event with custom ripple effect
        item.addEventListener('click', function(e) {
            // Create ripple effect immediately
            const rippleContainer = this.querySelector('.bottom-nav-ripple-container');
            const ripple = this.querySelector('.bottom-nav-ripple');

            // Remove any existing animation class
            ripple.classList.remove('animate');

            // Set ripple position based on click coordinates
            const rect = rippleContainer.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            ripple.style.left = `${x}px`;
            ripple.style.top = `${y}px`;

            // Force reflow to ensure the animation restarts
            void ripple.offsetWidth;

            // Add animation class immediately
            requestAnimationFrame(() => {
                ripple.classList.add('animate');
            });

            // Remove active class from all items
            bottomNavItems.forEach(navItem => {
                navItem.classList.remove('active');
            });

            // Add active class to clicked item
            this.classList.add('active');

            // Get the navigation item text
            const navText = this.querySelector('.bottom-nav-label').textContent;
            console.log(`Navigated to: ${navText}`);

            // In a real app, you would navigate to the corresponding page
            // For demo purposes, we'll just show a message
            if (navText !== 'Dashboard') {
                // alert(`This would navigate to the ${navText} page in a real app.`);
            }
        });
    });

    // Implement scroll-aware top app bar
    const topAppBar = document.querySelector('.app-bar');
    let lastScrollTop = 0;
    let scrollThreshold = 10; // Minimum scroll amount before showing/hiding
    let isScrolling = false;

    // Add scroll event listener to dashboard content
    dashboardContent.addEventListener('scroll', function() {
        if (!isScrolling) {
            window.requestAnimationFrame(function() {
                // handleScroll(dashboardContent.scrollTop);
                isScrolling = false;
            });
            isScrolling = true;
        }
    });

    function handleScroll(scrollTop) {
        // Determine scroll direction
        if (Math.abs(scrollTop - lastScrollTop) < scrollThreshold) {
            return; // Don't do anything for small scroll amounts
        }

        const wasHidden = topAppBar.classList.contains('app-bar-hidden');

        if (scrollTop > lastScrollTop && scrollTop > 60) {
            // Scrolling down & past the threshold - hide the app bar
            if (!wasHidden) {
                topAppBar.classList.add('app-bar-hidden');
            }
        } else {
            // Scrolling up or at the top - show the app bar
            if (wasHidden) {
                topAppBar.classList.remove('app-bar-hidden');

                // Adjust scroll position to prevent content jump
                if (scrollTop > 0) {
                    // Only adjust if not at the top
                    const appBarHeight = parseInt(getComputedStyle(document.documentElement)
                        .getPropertyValue('--top-app-bar-height'));
                    dashboardContent.scrollTop = scrollTop - appBarHeight;
                }
            }
        }

        lastScrollTop = scrollTop;
    }

    // Initialize form components if on forms page
    if (document.querySelector('.mdc-text-field')) {
        // Initialize text fields
        const textFields = document.querySelectorAll('.mdc-text-field');
        textFields.forEach(textField => new mdc.textField.MDCTextField(textField));

        // Initialize select menus
        const selects = document.querySelectorAll('.mdc-select');
        selects.forEach(select => new mdc.select.MDCSelect(select));

        // Initialize checkboxes
        const checkboxes = document.querySelectorAll('.mdc-checkbox');
        checkboxes.forEach(checkbox => new mdc.checkbox.MDCCheckbox(checkbox));

        // Initialize radio buttons
        const radios = document.querySelectorAll('.mdc-radio');
        radios.forEach(radio => new mdc.radio.MDCRadio(radio));

        // Initialize form field ripples
        const formFields = document.querySelectorAll('.mdc-form-field');
        formFields.forEach(field => new mdc.formField.MDCFormField(field));

        // Initialize switches
        try {
            const switches = document.querySelectorAll('.mdc-switch');
            switches.forEach(switchControl => {
                // Make sure the switch has the required ripple element
                if (!switchControl.querySelector('.mdc-switch__ripple')) {
                    const ripple = document.createElement('div');
                    ripple.className = 'mdc-switch__ripple';
                    const thumbUnderlay = switchControl.querySelector('.mdc-switch__thumb-underlay');
                    if (thumbUnderlay && !thumbUnderlay.querySelector('.mdc-switch__ripple')) {
                        thumbUnderlay.appendChild(ripple);
                    }
                }
                new mdc.switchControl.MDCSwitch(switchControl);
            });
        } catch (error) {
            console.warn('Error initializing switches:', error);
        }

        // Initialize all buttons with ripple effect
        const buttons = document.querySelectorAll('.mdc-button, .mdc-fab');
        buttons.forEach(button => new mdc.ripple.MDCRipple(button));

        // Initialize dialog form functionality
        initializeDialogForm();

        // Initialize modal dialog functionality
        initializeModalDialog();
    }

    // Initialize Banner
    const bannerElement = document.getElementById('app-banner');
    if (bannerElement) {
        const banner = new mdc.banner.MDCBanner(bannerElement);
        const showBannerButton = document.getElementById('show-banner-button');
        const closeBannerButton = document.getElementById('close-banner-button');

        if (showBannerButton) {
            showBannerButton.addEventListener('click', () => {
                bannerElement.style.display = 'flex'; // Make banner visible before opening
                banner.open();
            });
        }
        if (closeBannerButton) {
            // The banner component handles the primary action click internally to close,
            // but you might add extra logic here if needed.
            // We can also listen for the closing event:
            banner.listen('MDCBanner:closing', (event) => {
                console.log('Banner closing:', event.detail.reason);
                // Optionally hide the element again after closing animation completes
                // Use setTimeout to wait for animation
                setTimeout(() => {
                    if (!banner.isOpen) { // Check if it's actually closed
                        bannerElement.style.display = 'none';
                    }
                }, 300); // Adjust timeout based on animation duration
            });
        }
    }

    // Initialize Example Snackbar
    const exampleSnackbarElement = document.getElementById('example-snackbar');
    let exampleSnackbar;
    if (exampleSnackbarElement) {
        exampleSnackbar = new mdc.snackbar.MDCSnackbar(exampleSnackbarElement);

        const showSnackbarBasic = document.getElementById('show-snackbar-basic');
        const showSnackbarAction = document.getElementById('show-snackbar-action');
        const showSnackbarLeading = document.getElementById('show-snackbar-leading');

        if (showSnackbarBasic) {
            showSnackbarBasic.addEventListener('click', () => {
                exampleSnackbar.labelText = 'This is a basic snackbar message.';
                exampleSnackbar.timeoutMs = 4000; // Default
                exampleSnackbar.leading = false;
                // Ensure action button text is cleared if previously set
                const actionButton = exampleSnackbarElement.querySelector('.mdc-snackbar__action');
                if (actionButton) actionButton.textContent = '';
                exampleSnackbar.open();
            });
        }

        if (showSnackbarAction) {
            showSnackbarAction.addEventListener('click', () => {
                exampleSnackbar.labelText = 'Snackbar with an action button.';
                exampleSnackbar.timeoutMs = 7000; // Longer timeout for actions
                exampleSnackbar.leading = false;
                // Set action button text
                const actionButton = exampleSnackbarElement.querySelector('.mdc-snackbar__action .mdc-button__label');
                if (actionButton) actionButton.textContent = 'Retry';
                exampleSnackbar.open();
                // Optional: Listen for action click
                exampleSnackbar.listen('MDCSnackbar:closing', (event) => {
                    if (event.detail.reason === 'action') {
                        console.log('Snackbar action clicked!');
                        // Add retry logic here
                    }
                });
            });
        }

        if (showSnackbarLeading) {
            showSnackbarLeading.addEventListener('click', () => {
                exampleSnackbar.labelText = 'This is a leading snackbar.';
                exampleSnackbar.timeoutMs = 4000;
                exampleSnackbar.leading = true;
                // Ensure action button text is cleared
                const actionButton = exampleSnackbarElement.querySelector('.mdc-snackbar__action');
                if (actionButton) actionButton.textContent = '';
                exampleSnackbar.open();
            });
        }
    }

    // Initialize Tabs
    const tabBarElement = document.getElementById('content-tabs');
    if (tabBarElement) {
        const tabBar = new mdc.tabBar.MDCTabBar(tabBarElement);
        const contentEls = document.querySelectorAll('.tab-content');

        tabBar.listen('MDCTabBar:activated', (event) => {
            const index = event.detail.index;
            // Hide all content panels
            contentEls.forEach(el => el.style.display = 'none');
            // Show the activated panel
            const activePanelId = `tab-content-${['home', 'settings', 'about'][index]}`; // Map index to ID
            const activePanel = document.getElementById(activePanelId);
            if (activePanel) {
                activePanel.style.display = 'block';
            }
        });
    }

    // --- Dialog Logic ---
    // Only initialize dialogs if they exist in the DOM
    const alertDialogElement = document.querySelector('#alert-dialog');
    const confirmationDialogElement = document.querySelector('#confirmation-dialog');
    const simpleDialogElement = document.querySelector('#simple-dialog');

    let alertDialog, confirmationDialog, simpleDialog;

    if (alertDialogElement) {
        alertDialog = new mdc.dialog.MDCDialog(alertDialogElement);
    }

    if (confirmationDialogElement) {
        confirmationDialog = new mdc.dialog.MDCDialog(confirmationDialogElement);
    }

    if (simpleDialogElement) {
        simpleDialog = new mdc.dialog.MDCDialog(simpleDialogElement);
    }

    const showAlertButton = document.querySelector('#show-alert-dialog-button');
    const showConfirmationButton = document.querySelector('#show-confirmation-dialog-button');
    const showSimpleButton = document.querySelector('#show-simple-dialog-button');

    if (showAlertButton && alertDialog) {
        showAlertButton.addEventListener('click', () => {
            alertDialog.open();
        });
    }

    if (showConfirmationButton && confirmationDialog) {
        showConfirmationButton.addEventListener('click', () => {
            confirmationDialog.open();
        });
    }

    if (showSimpleButton && simpleDialog) {
        showSimpleButton.addEventListener('click', () => {
            simpleDialog.open();
        });
    }

    // Optional: Listen for closing events to see which action was taken
    if (alertDialog) {
        alertDialog.listen('MDCDialog:closed', (event) => {
            console.log(`Alert Dialog closed with action: ${event.detail.action}`);
        });
    }

    if (confirmationDialog) {
        confirmationDialog.listen('MDCDialog:closed', (event) => {
            console.log(`Confirmation Dialog closed with action: ${event.detail.action}`);
            if (event.detail.action === 'accept') {
                console.log('User confirmed the action.');
                // Add logic for accepted action here
            }
        });
    }

    if (simpleDialog) {
        simpleDialog.listen('MDCDialog:closed', (event) => {
            console.log(`Simple Dialog closed with action: ${event.detail.action}`);
            if (event.detail.action && event.detail.action !== 'close') { // Check if an item was clicked
                console.log(`Selected account: ${event.detail.action}`);
                // Add logic for selected item here
            }
        });
    }

    // Registration Dialog
    const registrationDialog = new mdc.dialog.MDCDialog(document.querySelector('#registration-dialog'));
    const showRegistrationButton = document.querySelector('#show-registration-dialog-button');
    showRegistrationButton.addEventListener('click', () => registrationDialog.open());
    registrationDialog.listen('MDCDialog:closed', (event) => {
        console.log(`Registration Dialog closed with action: ${event.detail.action}`);
        if (event.detail.action === 'register') {
            // Collect registration form data
            const name = document.getElementById('reg-name').value;
            const email = document.getElementById('reg-email').value;
            const password = document.getElementById('reg-password').value;
            const confirmPassword = document.getElementById('reg-confirm-password').value;
            if (password !== confirmPassword) {
                console.error('Passwords do not match');
                return;
            }
            console.log('Registration submitted:', { name, email, password });
            // Here you can send data to the server
            // Reset form
            const regForm = document.getElementById('registration-form');
            if (regForm) regForm.reset();
        }
    });

    // Product Create Dialog
    const productDialog = new mdc.dialog.MDCDialog(document.querySelector('#product-dialog'));
    const showProductButton = document.querySelector('#show-product-dialog-button');
    showProductButton.addEventListener('click', () => productDialog.open());
    productDialog.listen('MDCDialog:closed', (event) => {
        console.log(`Product Dialog closed with action: ${event.detail.action}`);
        if (event.detail.action === 'save') {
            // Validate form before processing
            const productForm = document.getElementById('product-form');
            if (!productForm.checkValidity()) {
                productForm.reportValidity();
                return;
            }
            // Collect form data
            const name = document.getElementById('product-name').value;
            const price = document.getElementById('product-price').value;
            const category = document.querySelector('#product-dialog .mdc-select__selected-text').textContent;
            console.log('Product created:', { name, price, category });
            // Reset form
            productForm.reset();
        }
    });
});

// Function to initialize dialog form
function initializeDialogForm() {
    const dialogFormContainer = document.getElementById('dialog-form-container');
    const dialogFormBackdrop = document.getElementById('dialog-form-backdrop');
    const openDialogFormButton = document.getElementById('show-dialog-form-button');
    const closeDialogFormButton = document.getElementById('close-dialog-form-button');
    const dialogFormCancelButton = document.getElementById('dialog-form-cancel-button');
    const dialogFormSaveButton = document.getElementById('dialog-form-save-button');
    const productForm = document.getElementById('dialog-product-form');

    // If dialog form elements don't exist, return early
    if (!dialogFormContainer || !openDialogFormButton) return;

    // Function to open dialog form
    function openDialogForm() {
        dialogFormContainer.classList.add('open');
        document.body.style.overflow = 'hidden'; // Prevent scrolling behind dialog

        // Re-initialize form components inside dialog to ensure proper rendering
        const dialogTextFields = dialogFormContainer.querySelectorAll('.mdc-text-field');
        dialogTextFields.forEach(textField => {
            new mdc.textField.MDCTextField(textField);
        });

        const dialogSelects = dialogFormContainer.querySelectorAll('.mdc-select');
        dialogSelects.forEach(select => {
            new mdc.select.MDCSelect(select);
        });

        const dialogCheckboxes = dialogFormContainer.querySelectorAll('.mdc-checkbox');
        dialogCheckboxes.forEach(checkbox => {
            new mdc.checkbox.MDCCheckbox(checkbox);
        });

        const dialogSwitches = dialogFormContainer.querySelectorAll('.mdc-switch');
        dialogSwitches.forEach(switchEl => {
            // Make sure the switch has the required ripple element
            if (!switchEl.querySelector('.mdc-switch__ripple')) {
                const ripple = document.createElement('div');
                ripple.className = 'mdc-switch__ripple';
                const thumbUnderlay = switchEl.querySelector('.mdc-switch__thumb-underlay');
                if (thumbUnderlay && !thumbUnderlay.querySelector('.mdc-switch__ripple')) {
                    thumbUnderlay.appendChild(ripple);
                }
            }
            try {
                new mdc.switchControl.MDCSwitch(switchEl);
            } catch (error) {
                console.warn('Error initializing switch in dialog:', error);
            }
        });
    }

    // Function to close dialog form with animation
    function closeDialogForm() {
        dialogFormContainer.classList.add('closing');
        document.body.style.overflow = ''; // Restore scrolling

        // Wait for animation to complete before hiding
        setTimeout(() => {
            dialogFormContainer.classList.remove('open');
            dialogFormContainer.classList.remove('closing');

            // Reset form
            if (productForm) {
                productForm.reset();
            }
        }, 300); // Match with CSS animation duration
    }

    // Open dialog form when button is clicked
    openDialogFormButton.addEventListener('click', openDialogForm);

    // Close dialog form when close button is clicked
    if (closeDialogFormButton) {
        closeDialogFormButton.addEventListener('click', closeDialogForm);
    }

    // Close dialog form when cancel button is clicked
    if (dialogFormCancelButton) {
        dialogFormCancelButton.addEventListener('click', closeDialogForm);
    }

    // Close dialog form when backdrop is clicked
    if (dialogFormBackdrop) {
        dialogFormBackdrop.addEventListener('click', closeDialogForm);
    }

    // Handle form submission
    if (dialogFormSaveButton && productForm) {
        dialogFormSaveButton.addEventListener('click', function(e) {
            e.preventDefault();

            // Check form validity
            const isValid = productForm.checkValidity();
            if (!isValid) {
                // Trigger browser's native validation UI
                productForm.reportValidity();
                return;
            }

            // Collect form data
            const formData = {
                name: document.getElementById('dialog-product-name').value,
                description: document.getElementById('dialog-product-description').value,
                price: document.getElementById('dialog-product-price').value,
                category: dialogFormContainer.querySelector('.mdc-select__selected-text').textContent,
                stock: document.getElementById('dialog-product-stock').value,
                active: document.getElementById('dialog-product-active').checked,
                featured: document.getElementById('dialog-product-featured').checked,
                tags: document.getElementById('dialog-product-tags').value,
                notes: document.getElementById('dialog-product-notes').value
            };

            // Log form data (in a real app, you would send this to a server)
            console.log('Product form submitted with data:', formData);

            // Show success message
            const snackbar = document.createElement('div');
            snackbar.className = 'mdc-snackbar mdc-snackbar--open';
            snackbar.innerHTML = `
                <div class="mdc-snackbar__surface" role="status" aria-relevant="additions">
                    <div class="mdc-snackbar__label" aria-atomic="false">
                        Product successfully added!
                    </div>
                    <div class="mdc-snackbar__actions" aria-atomic="true">
                        <button type="button" class="mdc-button mdc-snackbar__action">
                            <div class="mdc-button__ripple"></div>
                            <span class="mdc-button__label">OK</span>
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(snackbar);

            // Initialize snackbar
            const mdcSnackbar = new mdc.snackbar.MDCSnackbar(snackbar);
            mdcSnackbar.timeoutMs = 5000;
            mdcSnackbar.open();

            // Remove snackbar after it closes
            mdcSnackbar.listen('MDCSnackbar:closed', () => {
                document.body.removeChild(snackbar);
            });

            // Close dialog form
            closeDialogForm();
        });
    }

    // Close dialog form when Escape key is pressed
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && dialogFormContainer.classList.contains('open')) {
            closeDialogForm();
        }
    });
}

// Function to initialize modal dialog
function initializeModalDialog() {
    const modalContainer = document.getElementById('modal-container');
    const modalBackdrop = document.getElementById('modal-backdrop');
    const openModalButton = document.getElementById('open-modal-button');
    const closeModalButton = document.getElementById('close-modal-button');
    const modalCancelButton = document.getElementById('modal-cancel-button');
    const modalSaveButton = document.getElementById('modal-save-button');
    const modalForm = document.getElementById('modal-form');

    // If modal elements don't exist, return early
    if (!modalContainer || !openModalButton) return;

    // Function to open modal
    function openModal() {
        modalContainer.classList.add('open');
        document.body.style.overflow = 'hidden'; // Prevent scrolling behind modal

        // Re-initialize form components inside modal to ensure proper rendering
        const modalTextFields = modalContainer.querySelectorAll('.mdc-text-field');
        modalTextFields.forEach(textField => {
            new mdc.textField.MDCTextField(textField);
        });

        const modalSelects = modalContainer.querySelectorAll('.mdc-select');
        modalSelects.forEach(select => {
            new mdc.select.MDCSelect(select);
        });

        const modalCheckboxes = modalContainer.querySelectorAll('.mdc-checkbox');
        modalCheckboxes.forEach(checkbox => {
            new mdc.checkbox.MDCCheckbox(checkbox);
        });

        const modalSwitches = modalContainer.querySelectorAll('.mdc-switch');
        modalSwitches.forEach(switchEl => {
            new mdc.switchControl.MDCSwitch(switchEl);
        });
    }

    // Function to close modal with animation
    function closeModal() {
        modalContainer.classList.add('closing');
        document.body.style.overflow = ''; // Restore scrolling

        // Wait for animation to complete before hiding
        setTimeout(() => {
            modalContainer.classList.remove('open');
            modalContainer.classList.remove('closing');

            // Reset form
            if (modalForm) {
                modalForm.reset();
            }
        }, 300); // Match with CSS animation duration
    }

    // Open modal when button is clicked
    openModalButton.addEventListener('click', openModal);

    // Close modal when close button is clicked
    if (closeModalButton) {
        closeModalButton.addEventListener('click', closeModal);
    }

    // Close modal when cancel button is clicked
    if (modalCancelButton) {
        modalCancelButton.addEventListener('click', closeModal);
    }

    // Close modal when backdrop is clicked
    if (modalBackdrop) {
        modalBackdrop.addEventListener('click', closeModal);
    }

    // Handle form submission
    if (modalSaveButton && modalForm) {
        modalSaveButton.addEventListener('click', function(e) {
            e.preventDefault();

            // Check form validity
            const isValid = modalForm.checkValidity();
            if (!isValid) {
                // Trigger browser's native validation UI
                modalForm.reportValidity();
                return;
            }

            // Collect form data
            const formData = {
                name: document.getElementById('modal-name').value,
                email: document.getElementById('modal-email').value,
                role: modalContainer.querySelector('.mdc-select__selected-text').textContent,
                active: document.getElementById('modal-active').checked,
                sendWelcomeEmail: document.getElementById('modal-welcome-email').checked
            };

            // Log form data (in a real app, you would send this to a server)
            console.log('Form submitted with data:', formData);

            // Show success message
            const snackbar = document.createElement('div');
            snackbar.className = 'mdc-snackbar mdc-snackbar--open';
            snackbar.innerHTML = `
                <div class="mdc-snackbar__surface" role="status" aria-relevant="additions">
                    <div class="mdc-snackbar__label" aria-atomic="false">
                        User successfully added!
                    </div>
                    <div class="mdc-snackbar__actions" aria-atomic="true">
                        <button type="button" class="mdc-button mdc-snackbar__action">
                            <div class="mdc-button__ripple"></div>
                            <span class="mdc-button__label">OK</span>
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(snackbar);

            // Initialize snackbar
            const mdcSnackbar = new mdc.snackbar.MDCSnackbar(snackbar);
            mdcSnackbar.timeoutMs = 5000;
            mdcSnackbar.open();

            // Remove snackbar after it closes
            mdcSnackbar.listen('MDCSnackbar:closed', () => {
                document.body.removeChild(snackbar);
            });

            // Close modal
            closeModal();
        });
    }

    // Close modal when Escape key is pressed
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modalContainer.classList.contains('open')) {
            closeModal();
        }
    });
}