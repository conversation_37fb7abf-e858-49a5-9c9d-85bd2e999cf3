{% load static %}
{% load pwa %}
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EcolePro - Connexion</title>

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Material Components Web CSS -->
    <link href="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'material/css/styles.css' %}" rel="stylesheet">
    <link href="{% static 'material/css/login.css' %}" rel="stylesheet">

    <!-- Progressive Web App Meta -->
    {% progressive_web_app_meta %}
</head>
<body class="login-body">
    <!-- Preloader -->
    <div class="preloader">
        <div class="spinner">
            <svg class="circular" viewBox="25 25 50 50">
                <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="3" stroke-miterlimit="10"/>
            </svg>
        </div>
    </div>

    <div class="login-container">
        <div class="login-card mdc-card" id="login-form">
            <div class="login-header">
                <div class="login-logo">
                    <span class="material-icons">school</span>
                </div>
                <h1 class="login-title">EcolePro</h1>
                <h2 class="login-subtitle">Connectez-vous pour continuer</h2>
            </div>

            <div class="login-form">
                <form method="post" action="{{ request.path }}" onsubmit="if (this.checkValidity()) { document.getElementById('login-button').disabled = true; document.querySelector('#login-button .mdc-button__label').textContent = 'Connexion en cours...'; }">
                    {% csrf_token %}

                    <!-- School Year Field -->
                    <div class="mdc-select mdc-select--filled mdc-field--with-leading-icon" id="year-field" style="width: 100%; margin-bottom: 24px">
                        <div class="mdc-select__anchor">
                            <span class="mdc-select__ripple"></span>
                            <span class="material-icons mdc-select__icon">calendar_today</span>
                            <span class="mdc-select__selected-text"></span>
                            <span class="mdc-select__dropdown-icon">
                                <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                    <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                    <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                </svg>
                            </span>
                            <span class="mdc-floating-label mdc-floating-label--float-above">Année scolaire</span>
                            <span class="mdc-line-ripple"></span>
                        </div>
                        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                            <ul class="mdc-list">
                                {% for year in years %}
                                <li class="mdc-list-item {% if year.active %}mdc-list-item--selected{% endif %}"
                                    data-value="{{ year.id }}"
                                    aria-selected="{% if year.active %}true{% else %}false{% endif %}">
                                    <span class="mdc-list-item__ripple"></span>
                                    <span class="mdc-list-item__text">{{ year }}</span>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        <input type="hidden" name="year" id="id_year" required>
                    </div>

                    <!-- Username Field -->
                    <div class="mdc-text-field mdc-text-field--filled login-field mdc-field--with-leading-icon" id="username-field">
                        <span class="mdc-text-field__ripple"></span>
                        <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">person</span>
                        <input class="mdc-text-field__input" type="text" id="id_username" name="username" aria-labelledby="username-label" required>
                        <label class="mdc-floating-label" id="username-label">Nom d'utilisateur</label>
                        <span class="mdc-line-ripple"></span>
                    </div>

                    <!-- Password Field -->
                    <div class="mdc-text-field mdc-text-field--filled login-field mdc-field--with-leading-icon" id="password-field">
                        <span class="mdc-text-field__ripple"></span>
                        <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">lock</span>
                        <input class="mdc-text-field__input" type="password" id="id_password" name="password" aria-labelledby="password-label" required>
                        <label class="mdc-floating-label" id="password-label">Mot de passe</label>
                        <span class="mdc-line-ripple"></span>
                        <button type="button" class="material-icons mdc-text-field__icon mdc-text-field__icon--trailing password-toggle ripple" tabindex="0" role="button" style="border: none; height: auto">visibility_off</button>
                    </div>

                    <!-- Remember Me Checkbox -->
                    <div class="login-options">
                        <div class="mdc-form-field">
                            <div class="mdc-checkbox">
                                <input type="checkbox" class="mdc-checkbox__native-control" id="id_remember" name="remember"/>
                                <div class="mdc-checkbox__background">
                                    <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                        <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                    </svg>
                                    <div class="mdc-checkbox__mixedmark"></div>
                                </div>
                                <div class="mdc-checkbox__ripple"></div>
                            </div>
                            <label for="id_remember">Se souvenir de moi</label>
                        </div>

                        <a href="{% url 'users:password_reset' %}" hx-push-url="{% url 'users:password_reset' %}"
                           class="forgot-password"
                           hx-get="{% url 'users:password_reset' %}" hx-push-url="{% url 'users:password_reset' %}"
                           hx-target="#login-form"
                           hx-swap="innerHTML">Mot de passe oublié?</a>
                    </div>

                    <!-- Error Messages -->
                    {% if form.password.errors %}
                    <div class="login-error">
                        <span class="material-icons">error_outline</span>
                        <span>{{ form.password.errors|first }}</span>
                    </div>
                    {% elif form.errors %}
                    <div class="login-error">
                        <span class="material-icons">error_outline</span>
                        <span>Nom d'utilisateur ou mot de passe incorrect.</span>
                    </div>
                    {% endif %}

                    <!-- Login Button -->
                    <button type="submit" class="mdc-button mdc-button--raised login-button" id="login-button">
                        <span class="mdc-button__ripple"></span>
                        <span class="mdc-button__label">Se connecter</span>
                    </button>

                    <!-- Create Account Link -->
                    <div class="text-center pt-3">
                        <p>Vous êtes nouveau?</p>
                        <a class="mdc-button mdc-button--outlined" href="" hx-get="{% url 'school:test_school' %}" hx-target="#login-form">
                            <span class="mdc-button__ripple"></span>
                            <span class="material-icons mdc-button__icon">add_circle</span>
                            <span class="mdc-button__label">Enregistrer votre école</span>
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Footer -->
        <div class="login-footer">
            <div class="footer-copyright">
                © {% now "Y" %} EcolePro. Tous droits réservés.
            </div>
            <div class="footer-contact">
                <span class="material-icons" style="font-size: 16px; vertical-align: middle; color: #28a745;">phone</span>
                +225 07 59 95 14 53 / 05 45 84 55 98
            </div>
            <div class="install-app mt-2">
                <button class="mdc-button mdc-button--outlined" id="install">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons mdc-button__icon">download</span>
                    <span class="mdc-button__label">Installer l'application</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Material Components Web JavaScript -->
    <script src="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.js"></script>

    <!-- HTMX for dynamic content loading -->
    <script src="{% static 'js/htmx.min.js' %}"></script>

    <!-- Custom JavaScript -->
    <script>
        // Function to initialize Material Design components
        function initMaterialComponents() {
            // Initialize Material Components
            const textFields = document.querySelectorAll('.mdc-text-field');
            textFields.forEach(textField => {
                mdc.textField.MDCTextField.attachTo(textField);
            });

            // Initialize the Material Design select component
            const yearSelect = document.querySelector('.mdc-select');
            if (yearSelect) {
                const select = new mdc.select.MDCSelect(yearSelect);

                // Set the initial selected value
                const activeYear = document.querySelector('.mdc-list-item--selected');
                if (activeYear) {
                    select.value = activeYear.dataset.value;
                }

                // Update the hidden input when the selection changes
                select.listen('MDCSelect:change', () => {
                    document.getElementById('id_year').value = select.value;
                });
            }

            const checkboxes = document.querySelectorAll('.mdc-checkbox');
            checkboxes.forEach(checkbox => {
                mdc.checkbox.MDCCheckbox.attachTo(checkbox);
            });

            // Password toggle functionality
            const passwordToggle = document.querySelector('.password-toggle');
            const passwordField = document.getElementById('id_password');

            if (passwordToggle && passwordField) {
                passwordToggle.addEventListener('click', function() {
                    if (passwordField.type === 'password') {
                        passwordField.type = 'text';
                        this.textContent = 'visibility';
                    } else {
                        passwordField.type = 'password';
                        this.textContent = 'visibility_off';
                    }
                });
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initMaterialComponents();

            // PWA Install button
            const installButton = document.getElementById('install');

            if (installButton) {
                // Hide the install button by default
                installButton.style.display = 'none';

                let deferredPrompt;

                window.addEventListener('beforeinstallprompt', (e) => {
                    // Prevent Chrome 67 and earlier from automatically showing the prompt
                    e.preventDefault();
                    // Stash the event so it can be triggered later
                    deferredPrompt = e;
                    // Show the install button
                    installButton.style.display = 'inline-flex';
                });

                installButton.addEventListener('click', (e) => {
                    // Hide the install button
                    installButton.style.display = 'none';
                    // Show the install prompt
                    deferredPrompt.prompt();
                    // Wait for the user to respond to the prompt
                    deferredPrompt.userChoice.then((choiceResult) => {
                        if (choiceResult.outcome === 'accepted') {
                            console.log('User accepted the install prompt');
                        } else {
                            console.log('User dismissed the install prompt');
                        }
                        deferredPrompt = null;
                    });
                });
            }

            // Hide preloader when page is loaded
            const preloader = document.querySelector('.preloader');
            if (preloader) {
                preloader.style.display = 'none';
            }
        });

        // Re-initialize after HTMX content swap
        document.addEventListener('htmx:afterSwap', function(event) {
            console.log('HTMX swap occurred:', event.detail.target);
            setTimeout(() => {
                initMaterialComponents();
            }, 50);
        });
    </script>
</body>
</html>
