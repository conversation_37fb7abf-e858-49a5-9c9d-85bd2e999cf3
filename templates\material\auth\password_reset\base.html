{% load static %}
{% load pwa %}
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EcolePro - Réinitialisation du mot de passe</title>

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Material Components Web CSS -->
    <link href="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'material/css/styles.css' %}" rel="stylesheet">
    <link href="{% static 'material/css/login.css' %}" rel="stylesheet">

    <!-- Progressive Web App Meta -->
    {% progressive_web_app_meta %}
    
    <style>
        .login-container {
            position: relative;
        }
        
        .reset-instructions {
            margin-bottom: 24px;
            color: var(--md-on-surface-variant);
            text-align: center;
        }
        
        .login-error {
            display: flex;
            align-items: center;
            background-color: var(--md-error-container);
            color: var(--md-on-error-container);
            padding: 12px 16px;
            border-radius: 4px;
            margin-bottom: 16px;
            font-size: 14px;
        }
        
        .login-error .material-icons {
            margin-right: 8px;
            color: var(--md-on-error-container);
        }
        
        .htmx-indicator {
            display: none;
        }
        
        .htmx-request .htmx-indicator {
            display: flex;
        }
        
        .loading-spinner {
            position: relative;
            width: 40px;
            height: 40px;
        }
        
        .loading-spinner .circular {
            animation: rotate 2s linear infinite;
            height: 100%;
            transform-origin: center center;
            width: 100%;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            margin: auto;
        }
        
        .loading-spinner .path {
            stroke-dasharray: 1, 200;
            stroke-dashoffset: 0;
            animation: dash 1.5s ease-in-out infinite;
            stroke: var(--md-primary);
            stroke-linecap: round;
        }
        
        @keyframes rotate {
            100% {
                transform: rotate(360deg);
            }
        }
        
        @keyframes dash {
            0% {
                stroke-dasharray: 1, 200;
                stroke-dashoffset: 0;
            }
            50% {
                stroke-dasharray: 89, 200;
                stroke-dashoffset: -35px;
            }
            100% {
                stroke-dasharray: 89, 200;
                stroke-dashoffset: -124px;
            }
        }
    </style>
</head>
<body class="login-body">
    <!-- Preloader -->
    <div class="preloader">
        <div class="spinner">
            <svg class="circular" viewBox="25 25 50 50">
                <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="3" stroke-miterlimit="10"/>
            </svg>
        </div>
    </div>

    <div class="login-container">
        <div class="login-card mdc-card">
            <!-- Password Reset Content -->
            {% block content %}
                {% include partial_template %}
            {% endblock %}
        </div>

        <!-- Footer -->
        <div class="login-footer">
            <div class="footer-links">
                <a href="#">Politique de confidentialité</a>
                <span class="footer-divider">•</span>
                <a href="#">Conditions d'utilisation</a>
                <span class="footer-divider">•</span>
                <a href="#">Aide</a>
            </div>
            <div class="footer-copyright">
                © {% now "Y" %} EcolePro. Tous droits réservés.
            </div>
            <div class="footer-contact">
                <span class="material-icons" style="font-size: 16px; vertical-align: middle; color: #28a745;">phone</span>
                +225 07 59 95 14 53 / 05 45 84 55 98
            </div>
        </div>
    </div>

    <!-- Material Components Web JavaScript -->
    <script src="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.js"></script>
    
    <!-- HTMX -->
    <script src="{% static 'js/htmx.min.js' %}"></script>

    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Hide preloader when page is loaded
            const preloader = document.querySelector('.preloader');
            if (preloader) {
                preloader.style.display = 'none';
            }
        });
    </script>
</body>
</html>
