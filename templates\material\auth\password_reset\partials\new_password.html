<!-- New Password Form Partial -->
<div class="login-form" id="password-reset-container">
    {% if validlink %}
    <div class="login-header">
        <div class="login-logo">
            <span class="material-icons">lock_reset</span>
        </div>
        <h1 class="login-title">EcolePro</h1>
        <h2 class="login-subtitle">Définir un nouveau mot de passe</h2>
    </div>

    <form hx-post="{% url 'users:password_reset_confirm' %}"
          hx-target="#login-form"
          hx-swap="innerHTML"
          hx-indicator="#form-indicator"
          hx-trigger="submit">
        {% csrf_token %}

        <!-- New Password Field -->
        <div class="mdc-text-field mdc-text-field--filled login-field" id="new-password-field">
            <span class="mdc-text-field__ripple"></span>
            <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">lock</span>
            <input class="mdc-text-field__input" type="password" id="id_new_password1" name="new_password1" aria-labelledby="new-password-label" required>
            <label class="mdc-floating-label" id="new-password-label">Nouveau mot de passe</label>
            <span class="mdc-line-ripple"></span>
            <button type="button" class="material-icons mdc-text-field__icon mdc-text-field__icon--trailing password-toggle-1 ripple" tabindex="0" role="button" style="border: none; height: auto">visibility_off</button>
        </div>

        <ul class="password-requirements">
            <li>Votre mot de passe ne peut pas être trop similaire à vos autres informations personnelles.</li>
            <li>Votre mot de passe doit contenir au moins 8 caractères.</li>
            <li>Votre mot de passe ne peut pas être un mot de passe couramment utilisé.</li>
            <li>Votre mot de passe ne peut pas être entièrement numérique.</li>
        </ul>

        <!-- Confirm Password Field -->
        <div class="mdc-text-field mdc-text-field--filled login-field" id="confirm-password-field">
            <span class="mdc-text-field__ripple"></span>
            <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">lock</span>
            <input class="mdc-text-field__input" type="password" id="id_new_password2" name="new_password2" aria-labelledby="confirm-password-label" required>
            <label class="mdc-floating-label" id="confirm-password-label">Confirmer le mot de passe</label>
            <span class="mdc-line-ripple"></span>
            <button type="button" class="material-icons mdc-text-field__icon mdc-text-field__icon--trailing password-toggle-2 ripple" tabindex="0" role="button" style="border: none; height: auto">visibility_off</button>
        </div>

        <!-- Error Messages -->
        {% if form.errors %}
        <div class="login-error">
            <span class="material-icons">error_outline</span>
            <span>
                {% if form.new_password1.errors %}
                    {{ form.new_password1.errors|first }}
                {% elif form.new_password2.errors %}
                    {{ form.new_password2.errors|first }}
                {% else %}
                    Les mots de passe ne correspondent pas ou ne respectent pas les exigences.
                {% endif %}
            </span>
        </div>
        {% endif %}

        <!-- Submit Button -->
        <button type="submit" class="mdc-button mdc-button--raised login-button" id="reset-button">
            <span class="mdc-button__ripple"></span>
            <span class="mdc-button__label">Changer mon mot de passe</span>
        </button>
    </form>
    {% else %}
    <div class="invalid-link-card">
        <span class="material-icons error-icon">error_outline</span>
        <h1 class="message-title">Session invalide</h1>
        <p class="message-text">
            La session de réinitialisation du mot de passe est invalide ou a expiré.
            Veuillez recommencer la procédure de réinitialisation.
        </p>

        <!-- Request New Reset Button -->
        <a href="{% url 'users:password_reset' %}"
           class="mdc-button mdc-button--raised"
           hx-get="{% url 'users:password_reset' %}"
           hx-target="#login-form"
           hx-swap="innerHTML">
            <span class="mdc-button__ripple"></span>
            <span class="material-icons mdc-button__icon">refresh</span>
            <span class="mdc-button__label">Recommencer la procédure</span>
        </a>
    </div>
    {% endif %}

    <!-- Loading Indicator -->
    <div id="form-indicator" class="htmx-indicator">
        <div class="loading-spinner">
            <svg class="circular" viewBox="25 25 50 50">
                <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="3" stroke-miterlimit="10"/>
            </svg>
        </div>
    </div>
</div>

<style>
    .password-requirements {
        margin-top: 8px;
        margin-bottom: 16px;
        font-size: 14px;
        color: var(--md-on-surface-variant);
        padding-left: 16px;
    }

    .password-requirements li {
        margin-bottom: 4px;
    }

    .invalid-link-card {
        text-align: center;
        padding: 32px;
    }

    .error-icon {
        font-size: 64px;
        color: var(--md-error);
        margin-bottom: 16px;
    }

    .message-title {
        font-size: 24px;
        font-weight: 500;
        margin-bottom: 16px;
        color: var(--md-on-surface);
    }

    .message-text {
        color: var(--md-on-surface-variant);
        margin-bottom: 24px;
        line-height: 1.5;
    }

    .htmx-indicator {
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.7);
        z-index: 100;
        justify-content: center;
        align-items: center;
    }

    .htmx-request .htmx-indicator {
        display: flex;
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        initPasswordPage();
    });

    document.addEventListener('htmx:afterSwap', function() {
        initPasswordPage();
    });

    function initPasswordPage() {
        // Initialize Material Components
        const textFields = document.querySelectorAll('#password-reset-container .mdc-text-field, #login-form .mdc-text-field');
        textFields.forEach(textField => {
            try {
                mdc.textField.MDCTextField.attachTo(textField);
            } catch (e) {
                console.error('Error initializing text field:', e);
            }
        });

        // Password toggle functionality for first password field
        const passwordToggle1 = document.querySelector('.password-toggle-1');
        const passwordField1 = document.getElementById('id_new_password1');

        if (passwordToggle1 && passwordField1) {
            passwordToggle1.addEventListener('click', function() {
                if (passwordField1.type === 'password') {
                    passwordField1.type = 'text';
                    this.textContent = 'visibility';
                } else {
                    passwordField1.type = 'password';
                    this.textContent = 'visibility_off';
                }
            });
        }

        // Password toggle functionality for second password field
        const passwordToggle2 = document.querySelector('.password-toggle-2');
        const passwordField2 = document.getElementById('id_new_password2');

        if (passwordToggle2 && passwordField2) {
            passwordToggle2.addEventListener('click', function() {
                if (passwordField2.type === 'password') {
                    passwordField2.type = 'text';
                    this.textContent = 'visibility';
                } else {
                    passwordField2.type = 'password';
                    this.textContent = 'visibility_off';
                }
            });
        }
    }
</script>
