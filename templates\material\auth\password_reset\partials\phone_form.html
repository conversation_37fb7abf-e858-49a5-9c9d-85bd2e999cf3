<!-- Phone Verification Form Partial -->
<div class="login-form" id="password-reset-container">
    <div class="login-header">
        <div class="login-logo">
            <span class="material-icons">lock_reset</span>
        </div>
        <h1 class="login-title">EcolePro</h1>
        <h2 class="login-subtitle">Réinitialisation du mot de passe</h2>
    </div>

    <p class="reset-instructions">
        Vous avez oublié votre mot de passe ? Entrez votre numéro de téléphone ci-dessous, et nous vous enverrons un code de vérification par SMS.
    </p>

    <form hx-post="{% url 'users:password_reset' %}"
          hx-target="#login-form"
          hx-swap="innerHTML"
          hx-indicator="#form-indicator"
          hx-trigger="submit">
        {% csrf_token %}

        <!-- Phone Field -->
        <div class="mdc-text-field mdc-text-field--filled login-field mdc-field--with-leading-icon" id="phone-field" style="width: 100%; margin-bottom: 24px;">
            <span class="mdc-text-field__ripple"></span>
            <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">phone</span>
            <input class="mdc-text-field__input" type="tel" id="id_phone" name="phone" aria-labelledby="phone-label" required placeholder="XXXXXXXX" maxlength="10">
            <label class="mdc-floating-label" id="phone-label">Numéro de téléphone</label>
            <span class="mdc-line-ripple"></span>
        </div>

        <!-- Error Messages -->
        {% if form.errors %}
        <div class="login-error">
            <span class="material-icons">error_outline</span>
            <span>{{ form.phone.errors|first|default:"Veuillez entrer un numéro de téléphone valide." }}</span>
        </div>
        {% endif %}

        <!-- Submit Button -->
        <button type="submit" class="mdc-button mdc-button--raised login-button" id="reset-button">
            <span class="mdc-button__ripple"></span>
            <span class="mdc-button__label">Recevoir le code par SMS</span>
        </button>

        <!-- Back to Login Link -->
        <div class="text-center pt-3">
            <a class="mdc-button" href="{% url 'users:login' %}">
                <span class="mdc-button__ripple"></span>
                <span class="material-icons mdc-button__icon">arrow_back</span>
                <span class="mdc-button__label">Retour à la connexion</span>
            </a>
        </div>
    </form>

    <!-- Loading Indicator -->
    <div id="form-indicator" class="htmx-indicator">
        <div class="loading-spinner">
            <svg class="circular" viewBox="25 25 50 50">
                <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="3" stroke-miterlimit="10"/>
            </svg>
        </div>
    </div>
</div>

<style>
    .htmx-indicator {
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.7);
        z-index: 100;
        justify-content: center;
        align-items: center;
    }

    .htmx-request .htmx-indicator {
        display: flex;
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
    }

    .login-field {
        margin-bottom: 24px;
    }

    .reset-instructions {
        margin-bottom: 24px;
        color: var(--md-on-surface-variant);
        text-align: center;
    }
</style>

<script>
    // Function to initialize Material Design components
    function initPhoneForm() {
        // Initialize Material Components
        const textFields = document.querySelectorAll('#password-reset-container .mdc-text-field');
        textFields.forEach(textField => {
            try {
                mdc.textField.MDCTextField.attachTo(textField);
            } catch (e) {
                console.error('Error initializing text field:', e);
            }
        });

        // Focus on the phone input field
        const phoneInput = document.getElementById('id_phone');
        if (phoneInput) {
            setTimeout(() => {
                phoneInput.focus();
            }, 100);
        }
    }

    // Initialize immediately
    initPhoneForm();

    // Also initialize on DOMContentLoaded (for regular page loads)
    document.addEventListener('DOMContentLoaded', initPhoneForm);

    // Re-initialize after HTMX swap
    document.addEventListener('htmx:afterSwap', initPhoneForm);
</script>
