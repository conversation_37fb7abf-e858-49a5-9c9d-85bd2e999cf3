<!-- Verification Code Form Partial -->
<div class="login-form" id="password-reset-container">
    <div class="login-header">
        <div class="login-logo">
            <span class="material-icons">sms</span>
        </div>
        <h1 class="login-title">EcolePro</h1>
        <h2 class="login-subtitle">Vérification du code</h2>
    </div>

    <p class="reset-instructions">
        Nous avons envoyé un code de vérification à 6 chiffres par SMS. Veuillez entrer ce code ci-dessous pour continuer.
    </p>

    <form hx-post="{% url 'users:password_reset_verify' %}"
          hx-target="#login-form"
          hx-swap="innerHTML"
          hx-indicator="#form-indicator"
          hx-trigger="submit">
        {% csrf_token %}

        <!-- Verification Code Field -->
        <div class="verification-code">
            <div class="mdc-text-field mdc-text-field--filled login-field" id="code-field">
                <span class="mdc-text-field__ripple"></span>
                <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">dialpad</span>
                <input class="mdc-text-field__input code-input" type="text" id="id_code" name="code" maxlength="6" pattern="[0-9]{6}" inputmode="numeric" aria-labelledby="code-label" required>
                <label class="mdc-floating-label" id="code-label">Code à 6 chiffres</label>
                <span class="mdc-line-ripple"></span>
            </div>
        </div>

        <!-- Error Messages -->
        {% if form.errors %}
        <div class="login-error">
            <span class="material-icons">error_outline</span>
            <span>{{ form.code.errors|first|default:"Code invalide. Veuillez réessayer." }}</span>
        </div>
        {% endif %}

        <!-- Submit Button -->
        <button type="submit" class="mdc-button mdc-button--raised login-button" id="verify-button">
            <span class="mdc-button__ripple"></span>
            <span class="mdc-button__label">Vérifier le code</span>
        </button>

        <!-- Timer and Resend -->
        <span class="timer" id="countdown-timer">Le code expire dans: <span id="countdown">15:00</span></span>
        <a href="{% url 'users:password_reset' %}"
           class="resend-link"
           hx-get="{% url 'users:password_reset' %}"
           hx-target="#login-form"
           hx-swap="innerHTML">Renvoyer un nouveau code</a>

        <!-- Back to Login Link -->
        <div class="text-center pt-3">
            <a class="mdc-button" href="{% url 'users:login' %}">
                <span class="mdc-button__ripple"></span>
                <span class="material-icons mdc-button__icon">arrow_back</span>
                <span class="mdc-button__label">Retour à la connexion</span>
            </a>
        </div>
    </form>

    <!-- Loading Indicator -->
    <div id="form-indicator" class="htmx-indicator">
        <div class="loading-spinner">
            <svg class="circular" viewBox="25 25 50 50">
                <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="3" stroke-miterlimit="10"/>
            </svg>
        </div>
    </div>
</div>

<style>
    .verification-code {
        display: flex;
        justify-content: center;
        margin-bottom: 24px;
    }

    .code-input {
        letter-spacing: 8px;
        font-size: 24px;
        text-align: center;
        font-weight: 500;
    }

    .resend-link {
        display: block;
        text-align: center;
        margin-top: 16px;
        color: var(--md-primary);
        text-decoration: none;
        font-size: 14px;
    }

    .resend-link:hover {
        text-decoration: underline;
    }

    .timer {
        display: block;
        text-align: center;
        margin-top: 8px;
        font-size: 14px;
        color: var(--md-on-surface-variant);
    }

    .htmx-indicator {
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.7);
        z-index: 100;
        justify-content: center;
        align-items: center;
    }

    .htmx-request .htmx-indicator {
        display: flex;
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        initVerificationPage();
    });

    document.addEventListener('htmx:afterSwap', function() {
        initVerificationPage();
    });

    function initVerificationPage() {
        // Initialize Material Components
        const textFields = document.querySelectorAll('#password-reset-container .mdc-text-field, #login-form .mdc-text-field');
        textFields.forEach(textField => {
            try {
                mdc.textField.MDCTextField.attachTo(textField);
            } catch (e) {
                console.error('Error initializing text field:', e);
            }
        });

        // Auto-focus the code input
        const codeInput = document.getElementById('id_code');
        if (codeInput) {
            codeInput.focus();
        }

        // Countdown timer
        const countdownElement = document.getElementById('countdown');
        if (countdownElement) {
            let minutes = 15;
            let seconds = 0;

            const updateCountdown = () => {
                countdownElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

                if (minutes === 0 && seconds === 0) {
                    clearInterval(countdownInterval);
                    countdownElement.parentElement.textContent = 'Le code a expiré. Veuillez demander un nouveau code.';
                    return;
                }

                if (seconds === 0) {
                    minutes--;
                    seconds = 59;
                } else {
                    seconds--;
                }
            };

            updateCountdown();
            const countdownInterval = setInterval(updateCountdown, 1000);
        }
    }
</script>
