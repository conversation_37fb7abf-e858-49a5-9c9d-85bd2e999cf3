{% load static %}
{% load pwa %}
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EcolePro - Mot de passe réinitialisé</title>

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Material Components Web CSS -->
    <link href="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'material/css/styles.css' %}" rel="stylesheet">
    <link href="{% static 'material/css/login.css' %}" rel="stylesheet">

    <!-- Progressive Web App Meta -->
    {% progressive_web_app_meta %}
    
    <style>
        .success-icon {
            font-size: 64px;
            color: var(--md-primary);
            margin-bottom: 16px;
        }
        
        .message-card {
            text-align: center;
            padding: 32px;
        }
        
        .message-title {
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 16px;
            color: var(--md-on-surface);
        }
        
        .message-text {
            color: var(--md-on-surface-variant);
            margin-bottom: 24px;
            line-height: 1.5;
        }
    </style>
</head>
<body class="login-body">
    <!-- Preloader -->
    <div class="preloader">
        <div class="spinner">
            <svg class="circular" viewBox="25 25 50 50">
                <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="3" stroke-miterlimit="10"/>
            </svg>
        </div>
    </div>

    <div class="login-container">
        <div class="login-card mdc-card">
            <div class="message-card">
                <span class="material-icons success-icon">task_alt</span>
                <h1 class="message-title">Mot de passe réinitialisé</h1>
                <p class="message-text">
                    Votre mot de passe a été réinitialisé avec succès. Vous pouvez maintenant vous connecter avec votre nouveau mot de passe.
                </p>
                
                <!-- Login Button -->
                <a href="{% url 'users:login' %}" class="mdc-button mdc-button--raised">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons mdc-button__icon">login</span>
                    <span class="mdc-button__label">Se connecter</span>
                </a>
            </div>
        </div>

        <!-- Footer -->
        <div class="login-footer">
            <div class="footer-links">
                <a href="#">Politique de confidentialité</a>
                <span class="footer-divider">•</span>
                <a href="#">Conditions d'utilisation</a>
                <span class="footer-divider">•</span>
                <a href="#">Aide</a>
            </div>
            <div class="footer-copyright">
                © {% now "Y" %} EcolePro. Tous droits réservés.
            </div>
            <div class="footer-contact">
                <span class="material-icons" style="font-size: 16px; vertical-align: middle; color: #28a745;">phone</span>
                +225 07 59 95 14 53 / 05 45 84 55 98
            </div>
        </div>
    </div>

    <!-- Material Components Web JavaScript -->
    <script src="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Hide preloader when page is loaded
            const preloader = document.querySelector('.preloader');
            if (preloader) {
                preloader.style.display = 'none';
            }
        });
    </script>
</body>
</html>
