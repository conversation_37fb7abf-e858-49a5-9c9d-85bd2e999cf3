{% load static %}
{% load pwa %}
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EcolePro - Définir un nouveau mot de passe</title>

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Material Components Web CSS -->
    <link href="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'material/css/styles.css' %}" rel="stylesheet">
    <link href="{% static 'material/css/login.css' %}" rel="stylesheet">

    <!-- Progressive Web App Meta -->
    {% progressive_web_app_meta %}

    <style>
        .invalid-link-card {
            text-align: center;
            padding: 32px;
        }

        .error-icon {
            font-size: 64px;
            color: var(--md-error);
            margin-bottom: 16px;
        }

        .message-title {
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 16px;
            color: var(--md-on-surface);
        }

        .message-text {
            color: var(--md-on-surface-variant);
            margin-bottom: 24px;
            line-height: 1.5;
        }

        .password-requirements {
            margin-top: 8px;
            margin-bottom: 16px;
            font-size: 14px;
            color: var(--md-on-surface-variant);
            padding-left: 16px;
        }

        .password-requirements li {
            margin-bottom: 4px;
        }
    </style>
</head>
<body class="login-body">
    <!-- Preloader -->
    <div class="preloader">
        <div class="spinner">
            <svg class="circular" viewBox="25 25 50 50">
                <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="3" stroke-miterlimit="10"/>
            </svg>
        </div>
    </div>

    <div class="login-container">
        <div class="login-card mdc-card">
            {% if validlink %}
            <div class="login-header">
                <div class="login-logo">
                    <span class="material-icons">lock_reset</span>
                </div>
                <h1 class="login-title">EcolePro</h1>
                <h2 class="login-subtitle">Définir un nouveau mot de passe</h2>
            </div>

            <div class="login-form">
                <form method="post">
                    {% csrf_token %}

                    <!-- New Password Field -->
                    <div class="mdc-text-field mdc-text-field--filled login-field" id="new-password-field">
                        <span class="mdc-text-field__ripple"></span>
                        <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">lock</span>
                        <input class="mdc-text-field__input" type="password" id="id_new_password1" name="new_password1" aria-labelledby="new-password-label" required>
                        <label class="mdc-floating-label" id="new-password-label">Nouveau mot de passe</label>
                        <span class="mdc-line-ripple"></span>
                        <button type="button" class="material-icons mdc-text-field__icon mdc-text-field__icon--trailing password-toggle-1 ripple" tabindex="0" role="button" style="border: none; height: auto">visibility_off</button>
                    </div>

                    <ul class="password-requirements">
                        <li>Votre mot de passe ne peut pas être trop similaire à vos autres informations personnelles.</li>
                        <li>Votre mot de passe doit contenir au moins 8 caractères.</li>
                        <li>Votre mot de passe ne peut pas être un mot de passe couramment utilisé.</li>
                        <li>Votre mot de passe ne peut pas être entièrement numérique.</li>
                    </ul>

                    <!-- Confirm Password Field -->
                    <div class="mdc-text-field mdc-text-field--filled login-field" id="confirm-password-field">
                        <span class="mdc-text-field__ripple"></span>
                        <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">lock</span>
                        <input class="mdc-text-field__input" type="password" id="id_new_password2" name="new_password2" aria-labelledby="confirm-password-label" required>
                        <label class="mdc-floating-label" id="confirm-password-label">Confirmer le mot de passe</label>
                        <span class="mdc-line-ripple"></span>
                        <button type="button" class="material-icons mdc-text-field__icon mdc-text-field__icon--trailing password-toggle-2 ripple" tabindex="0" role="button" style="border: none; height: auto">visibility_off</button>
                    </div>

                    <!-- Error Messages -->
                    {% if form.errors %}
                    <div class="login-error">
                        <span class="material-icons">error_outline</span>
                        <span>
                            {% if form.new_password1.errors %}
                                {{ form.new_password1.errors|first }}
                            {% elif form.new_password2.errors %}
                                {{ form.new_password2.errors|first }}
                            {% else %}
                                Les mots de passe ne correspondent pas ou ne respectent pas les exigences.
                            {% endif %}
                        </span>
                    </div>
                    {% endif %}

                    <!-- Submit Button -->
                    <button type="submit" class="mdc-button mdc-button--raised login-button" id="reset-button">
                        <span class="mdc-button__ripple"></span>
                        <span class="mdc-button__label">Changer mon mot de passe</span>
                    </button>
                </form>
            </div>
            {% else %}
            <div class="invalid-link-card">
                <span class="material-icons error-icon">error_outline</span>
                <h1 class="message-title">Session invalide</h1>
                <p class="message-text">
                    La session de réinitialisation du mot de passe est invalide ou a expiré.
                    Veuillez recommencer la procédure de réinitialisation.
                </p>

                <!-- Request New Reset Button -->
                <a href="{% url 'users:password_reset' %}" class="mdc-button mdc-button--raised">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons mdc-button__icon">refresh</span>
                    <span class="mdc-button__label">Recommencer la procédure</span>
                </a>
            </div>
            {% endif %}
        </div>

        <!-- Footer -->
        <div class="login-footer">
            <div class="footer-links">
                <a href="#">Politique de confidentialité</a>
                <span class="footer-divider">•</span>
                <a href="#">Conditions d'utilisation</a>
                <span class="footer-divider">•</span>
                <a href="#">Aide</a>
            </div>
            <div class="footer-copyright">
                © {% now "Y" %} EcolePro. Tous droits réservés.
            </div>
            <div class="footer-contact">
                <span class="material-icons" style="font-size: 16px; vertical-align: middle; color: #28a745;">phone</span>
                +225 07 59 95 14 53 / 05 45 84 55 98
            </div>
        </div>
    </div>

    <!-- Material Components Web JavaScript -->
    <script src="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Material Components
            const textFields = document.querySelectorAll('.mdc-text-field');
            textFields.forEach(textField => {
                mdc.textField.MDCTextField.attachTo(textField);
            });

            // Password toggle functionality for first password field
            const passwordToggle1 = document.querySelector('.password-toggle-1');
            const passwordField1 = document.getElementById('id_new_password1');

            if (passwordToggle1 && passwordField1) {
                passwordToggle1.addEventListener('click', function() {
                    if (passwordField1.type === 'password') {
                        passwordField1.type = 'text';
                        this.textContent = 'visibility';
                    } else {
                        passwordField1.type = 'password';
                        this.textContent = 'visibility_off';
                    }
                });
            }

            // Password toggle functionality for second password field
            const passwordToggle2 = document.querySelector('.password-toggle-2');
            const passwordField2 = document.getElementById('id_new_password2');

            if (passwordToggle2 && passwordField2) {
                passwordToggle2.addEventListener('click', function() {
                    if (passwordField2.type === 'password') {
                        passwordField2.type = 'text';
                        this.textContent = 'visibility';
                    } else {
                        passwordField2.type = 'password';
                        this.textContent = 'visibility_off';
                    }
                });
            }

            // Hide preloader when page is loaded
            const preloader = document.querySelector('.preloader');
            if (preloader) {
                preloader.style.display = 'none';
            }

            // Submit button loading state
            const form = document.querySelector('form');
            const resetButton = document.getElementById('reset-button');

            if (form && resetButton) {
                form.addEventListener('submit', function() {
                    if (this.checkValidity()) {
                        resetButton.disabled = true;
                        resetButton.querySelector('.mdc-button__label').textContent = 'Traitement en cours...';
                    }
                });
            }
        });
    </script>
</body>
</html>
