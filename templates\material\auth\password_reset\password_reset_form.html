{% load static %}
{% load pwa %}
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EcolePro - Réinitialisation du mot de passe</title>

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Material Components Web CSS -->
    <link href="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'material/css/styles.css' %}" rel="stylesheet">
    <link href="{% static 'material/css/login.css' %}" rel="stylesheet">

    <!-- Progressive Web App Meta -->
    {% progressive_web_app_meta %}
</head>
<body class="login-body">
    <!-- Preloader -->
    <div class="preloader">
        <div class="spinner">
            <svg class="circular" viewBox="25 25 50 50">
                <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="3" stroke-miterlimit="10"/>
            </svg>
        </div>
    </div>

    <div class="login-container">
        <div class="login-card mdc-card">
            <div class="login-header">
                <div class="login-logo">
                    <span class="material-icons">lock_reset</span>
                </div>
                <h1 class="login-title">EcolePro</h1>
                <h2 class="login-subtitle">Réinitialisation du mot de passe</h2>
            </div>

            <div class="login-form">
                <p class="reset-instructions">
                    Vous avez oublié votre mot de passe ? Entrez votre numéro de téléphone ci-dessous, et nous vous enverrons un code de vérification par SMS.
                </p>

                <form method="post">
                    {% csrf_token %}

                    <!-- Phone Field -->
                    <div class="mdc-text-field mdc-text-field--filled login-field" id="phone-field">
                        <span class="mdc-text-field__ripple"></span>
                        <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">phone</span>
                        <input class="mdc-text-field__input" type="tel" id="id_phone" name="phone" aria-labelledby="phone-label" required>
                        <label class="mdc-floating-label" id="phone-label">Numéro de téléphone</label>
                        <span class="mdc-line-ripple"></span>
                    </div>

                    <!-- Error Messages -->
                    {% if form.errors %}
                    <div class="login-error">
                        <span class="material-icons">error_outline</span>
                        <span>{{ form.phone.errors|first|default:"Veuillez entrer un numéro de téléphone valide." }}</span>
                    </div>
                    {% endif %}

                    <!-- Submit Button -->
                    <button type="submit" class="mdc-button mdc-button--raised login-button" id="reset-button">
                        <span class="mdc-button__ripple"></span>
                        <span class="mdc-button__label">Recevoir le code par SMS</span>
                    </button>

                    <!-- Back to Login Link -->
                    <div class="text-center pt-3">
                        <a class="mdc-button" href="{% url 'users:login' %}">
                            <span class="mdc-button__ripple"></span>
                            <span class="material-icons mdc-button__icon">arrow_back</span>
                            <span class="mdc-button__label">Retour à la connexion</span>
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Footer -->
        <div class="login-footer">
            <div class="footer-links">
                <a href="#">Politique de confidentialité</a>
                <span class="footer-divider">•</span>
                <a href="#">Conditions d'utilisation</a>
                <span class="footer-divider">•</span>
                <a href="#">Aide</a>
            </div>
            <div class="footer-copyright">
                © {% now "Y" %} EcolePro. Tous droits réservés.
            </div>
            <div class="footer-contact">
                <span class="material-icons" style="font-size: 16px; vertical-align: middle; color: #28a745;">phone</span>
                +225 07 59 95 14 53 / 05 45 84 55 98
            </div>
        </div>
    </div>

    <!-- Material Components Web JavaScript -->
    <script src="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Material Components
            const textFields = document.querySelectorAll('.mdc-text-field');
            textFields.forEach(textField => {
                mdc.textField.MDCTextField.attachTo(textField);
            });

            // Hide preloader when page is loaded
            const preloader = document.querySelector('.preloader');
            if (preloader) {
                preloader.style.display = 'none';
            }

            // Submit button loading state
            const form = document.querySelector('form');
            const resetButton = document.getElementById('reset-button');

            if (form && resetButton) {
                form.addEventListener('submit', function() {
                    if (this.checkValidity()) {
                        resetButton.disabled = true;
                        resetButton.querySelector('.mdc-button__label').textContent = 'Envoi en cours...';
                    }
                });
            }
        });
    </script>
</body>
</html>
