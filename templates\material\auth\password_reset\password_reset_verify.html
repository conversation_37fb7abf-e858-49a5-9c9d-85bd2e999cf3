{% load static %}
{% load pwa %}
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EcolePro - Vérification du code</title>

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Material Components Web CSS -->
    <link href="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'material/css/styles.css' %}" rel="stylesheet">
    <link href="{% static 'material/css/login.css' %}" rel="stylesheet">

    <!-- Progressive Web App Meta -->
    {% progressive_web_app_meta %}
    
    <style>
        .verification-code {
            display: flex;
            justify-content: center;
            margin-bottom: 24px;
        }
        
        .code-input {
            letter-spacing: 8px;
            font-size: 24px;
            text-align: center;
            font-weight: 500;
        }
        
        .resend-link {
            display: block;
            text-align: center;
            margin-top: 16px;
            color: var(--md-primary);
            text-decoration: none;
            font-size: 14px;
        }
        
        .resend-link:hover {
            text-decoration: underline;
        }
        
        .timer {
            display: block;
            text-align: center;
            margin-top: 8px;
            font-size: 14px;
            color: var(--md-on-surface-variant);
        }
    </style>
</head>
<body class="login-body">
    <!-- Preloader -->
    <div class="preloader">
        <div class="spinner">
            <svg class="circular" viewBox="25 25 50 50">
                <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="3" stroke-miterlimit="10"/>
            </svg>
        </div>
    </div>

    <div class="login-container">
        <div class="login-card mdc-card">
            <div class="login-header">
                <div class="login-logo">
                    <span class="material-icons">sms</span>
                </div>
                <h1 class="login-title">EcolePro</h1>
                <h2 class="login-subtitle">Vérification du code</h2>
            </div>

            <div class="login-form">
                <p class="reset-instructions">
                    Nous avons envoyé un code de vérification à 6 chiffres par SMS. Veuillez entrer ce code ci-dessous pour continuer.
                </p>
                
                <form method="post">
                    {% csrf_token %}
                    
                    <!-- Verification Code Field -->
                    <div class="verification-code">
                        <div class="mdc-text-field mdc-text-field--filled login-field" id="code-field">
                            <span class="mdc-text-field__ripple"></span>
                            <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">dialpad</span>
                            <input class="mdc-text-field__input code-input" type="text" id="id_code" name="code" maxlength="6" pattern="[0-9]{6}" inputmode="numeric" aria-labelledby="code-label" required>
                            <label class="mdc-floating-label" id="code-label">Code à 6 chiffres</label>
                            <span class="mdc-line-ripple"></span>
                        </div>
                    </div>

                    <!-- Error Messages -->
                    {% if form.errors %}
                    <div class="login-error">
                        <span class="material-icons">error_outline</span>
                        <span>{{ form.code.errors|first|default:"Code invalide. Veuillez réessayer." }}</span>
                    </div>
                    {% endif %}

                    <!-- Submit Button -->
                    <button type="submit" class="mdc-button mdc-button--raised login-button" id="verify-button">
                        <span class="mdc-button__ripple"></span>
                        <span class="mdc-button__label">Vérifier le code</span>
                    </button>
                    
                    <!-- Timer and Resend -->
                    <span class="timer" id="countdown-timer">Le code expire dans: <span id="countdown">15:00</span></span>
                    <a href="{% url 'users:password_reset' %}" class="resend-link">Renvoyer un nouveau code</a>
                    
                    <!-- Back to Login Link -->
                    <div class="text-center pt-3">
                        <a class="mdc-button" href="{% url 'users:login' %}">
                            <span class="mdc-button__ripple"></span>
                            <span class="material-icons mdc-button__icon">arrow_back</span>
                            <span class="mdc-button__label">Retour à la connexion</span>
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Footer -->
        <div class="login-footer">
            <div class="footer-links">
                <a href="#">Politique de confidentialité</a>
                <span class="footer-divider">•</span>
                <a href="#">Conditions d'utilisation</a>
                <span class="footer-divider">•</span>
                <a href="#">Aide</a>
            </div>
            <div class="footer-copyright">
                © {% now "Y" %} EcolePro. Tous droits réservés.
            </div>
            <div class="footer-contact">
                <span class="material-icons" style="font-size: 16px; vertical-align: middle; color: #28a745;">phone</span>
                +225 07 59 95 14 53 / 05 45 84 55 98
            </div>
        </div>
    </div>

    <!-- Material Components Web JavaScript -->
    <script src="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Material Components
            const textFields = document.querySelectorAll('.mdc-text-field');
            textFields.forEach(textField => {
                mdc.textField.MDCTextField.attachTo(textField);
            });
            
            // Auto-focus the code input
            const codeInput = document.getElementById('id_code');
            if (codeInput) {
                codeInput.focus();
            }
            
            // Countdown timer
            const countdownElement = document.getElementById('countdown');
            if (countdownElement) {
                let minutes = 15;
                let seconds = 0;
                
                const updateCountdown = () => {
                    countdownElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                    
                    if (minutes === 0 && seconds === 0) {
                        clearInterval(countdownInterval);
                        countdownElement.parentElement.textContent = 'Le code a expiré. Veuillez demander un nouveau code.';
                        return;
                    }
                    
                    if (seconds === 0) {
                        minutes--;
                        seconds = 59;
                    } else {
                        seconds--;
                    }
                };
                
                updateCountdown();
                const countdownInterval = setInterval(updateCountdown, 1000);
            }
            
            // Submit button loading state
            const form = document.querySelector('form');
            const verifyButton = document.getElementById('verify-button');
            
            if (form && verifyButton) {
                form.addEventListener('submit', function() {
                    if (this.checkValidity()) {
                        verifyButton.disabled = true;
                        verifyButton.querySelector('.mdc-button__label').textContent = 'Vérification en cours...';
                    }
                });
            }
            
            // Hide preloader when page is loaded
            const preloader = document.querySelector('.preloader');
            if (preloader) {
                preloader.style.display = 'none';
            }
        });
    </script>
</body>
</html>
