{% extends 'material/base.html' %}
{% load static %}

{% block page_title %}EcolePro | Tableau de bord{% endblock %}

{% block content %}
<h1>Tableau de bord</h1>

<!-- Stats Cards -->
<div class="stats-cards">
    <div class="mdc-card stats-card mdc-card--outlined">
        <div class="mdc-card__ripple"></div>
        <div class="card-content">
            <div class="card-icon">
                <span class="material-icons">people</span>
            </div>
            <div class="card-data">
                <h2>{{ total_students|default:"0" }}</h2>
                <p>Élèves inscrits</p>
            </div>
        </div>
    </div>

    <div class="mdc-card stats-card mdc-card--outlined">
        <div class="mdc-card__ripple"></div>
        <div class="card-content">
            <div class="card-icon">
                <span class="material-icons">school</span>
            </div>
            <div class="card-data">
                <h2>{{ total_classes|default:"0" }}</h2>
                <p>Classes</p>
            </div>
        </div>
    </div>

    <div class="mdc-card stats-card mdc-card--outlined">
        <div class="mdc-card__ripple"></div>
        <div class="card-content">
            <div class="card-icon">
                <span class="material-icons">person</span>
            </div>
            <div class="card-data">
                <h2>{{ total_teachers|default:"0" }}</h2>
                <p>Enseignants</p>
            </div>
        </div>
    </div>

    <div class="mdc-card stats-card mdc-card--outlined">
        <div class="mdc-card__ripple"></div>
        <div class="card-content">
            <div class="card-icon">
                <span class="material-icons">payments</span>
            </div>
            <div class="card-data">
                <h2>{{ total_payments|default:"0" }} FCFA</h2>
                <p>Paiements du mois</p>
            </div>
        </div>
    </div>
</div>

<!-- Recent Payments -->
<div class="mdc-card recent-transactions mdc-card--outlined">
    <div class="mdc-card__ripple"></div>
    <h2 class="card-title">Paiements récents</h2>
    <div class="mdc-data-table">
        <div class="mdc-data-table__table-container">
            <table class="mdc-data-table__table">
                <thead>
                    <tr class="mdc-data-table__header-row">
                        <th class="mdc-data-table__header-cell">ID</th>
                        <th class="mdc-data-table__header-cell">Élève</th>
                        <th class="mdc-data-table__header-cell">Montant</th>
                        <th class="mdc-data-table__header-cell">Type</th>
                        <th class="mdc-data-table__header-cell">Date</th>
                        <th class="mdc-data-table__header-cell">Statut</th>
                    </tr>
                </thead>
                <tbody class="mdc-data-table__content">
                    {% for payment in recent_payments %}
                    <tr class="mdc-data-table__row">
                        <td class="mdc-data-table__cell">#{{ payment.id }}</td>
                        <td class="mdc-data-table__cell">{{ payment.student.get_full_name }}</td>
                        <td class="mdc-data-table__cell">{{ payment.amount }} FCFA</td>
                        <td class="mdc-data-table__cell">{{ payment.get_payment_type_display }}</td>
                        <td class="mdc-data-table__cell">{{ payment.created_at|date:"d/m/Y H:i" }}</td>
                        <td class="mdc-data-table__cell"><span class="status-completed">Complété</span></td>
                    </tr>
                    {% empty %}
                    <tr class="mdc-data-table__row">
                        <td class="mdc-data-table__cell" colspan="6" style="text-align: center;">Aucun paiement récent</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="mdc-card recent-transactions mdc-card--outlined">
    <div class="mdc-card__ripple"></div>
    <h2 class="card-title">Activités récentes</h2>
    <div class="activity-list">
        {% for activity in recent_activities %}
        <div class="activity-item">
            <div class="activity-icon">
                <span class="material-icons">{{ activity.icon }}</span>
            </div>
            <div class="activity-details">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-description">{{ activity.description }}</div>
                <div class="activity-time">{{ activity.timestamp|date:"d/m/Y H:i" }}</div>
            </div>
        </div>
        {% empty %}
        <div class="activity-item">
            <div class="activity-details">
                <div class="activity-description">Aucune activité récente</div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Additional dashboard-specific JavaScript can be added here
    document.addEventListener('DOMContentLoaded', function() {
        // Example: Initialize a chart if needed
        // const ctx = document.getElementById('myChart').getContext('2d');
        // const myChart = new Chart(ctx, { ... });
    });
</script>
{% endblock %}
