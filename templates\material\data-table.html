<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS System - Data Tables</title>

    <!-- Material Icons and Roboto Font -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Material Components Web CSS -->
    <link href="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <!-- Material Design Preloader -->
    <div class="preloader">
        <div class="spinner">
            <svg class="circular" viewBox="25 25 50 50">
                <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="3" stroke-miterlimit="10"/>
            </svg>
        </div>
    </div>

    <div class="app-container">
        <!-- Top App Bar -->
        <header class="mdc-top-app-bar app-bar">
            <div class="mdc-top-app-bar__row">
                <section class="mdc-top-app-bar__section mdc-top-app-bar__section--align-start">
                    <button class="material-icons mdc-top-app-bar__navigation-icon mdc-icon-button" id="menu-button" aria-label="Menu">
                        <div class="mdc-icon-button__ripple"></div>
                        menu
                    </button>
                    <span class="mdc-top-app-bar__title">Data Tables</span>
                </section>
                <section class="mdc-top-app-bar__section mdc-top-app-bar__section--align-end">
                    <!-- Desktop Search Bar (visible on larger screens) -->
                    <div class="search-container desktop-search">
                        <div class="mdc-text-field mdc-text-field--filled mdc-text-field--with-leading-icon mdc-text-field--no-label search-field">
                            <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">search</span>
                            <input class="mdc-text-field__input" type="text" placeholder="Search...">
                            <span class="mdc-line-ripple"></span>
                        </div>
                    </div>

                    <!-- Mobile Search Icon (visible on smaller screens) -->
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button mobile-search-icon" id="mobile-search-button" aria-label="Search">
                        <div class="mdc-icon-button__ripple"></div>
                        search
                    </button>

                    <!-- Mobile Search Bar (hidden by default, shown when search icon is clicked) -->
                    <div class="mobile-search-container" id="mobile-search-container">
                        <div class="mobile-search-inner">
                            <span class="material-icons mobile-search-icon">search</span>
                            <input type="text" class="mobile-search-input" placeholder="Search...">
                            <button class="material-icons mdc-icon-button mobile-search-close" id="mobile-search-close">
                                <div class="mdc-icon-button__ripple"></div>
                                close
                            </button>
                        </div>
                    </div>
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" id="theme-toggle-button" aria-label="Toggle dark mode">
                        <div class="mdc-icon-button__ripple"></div>
                        light_mode
                    </button>
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" aria-label="Notifications" style="display: none;">
                        <div class="mdc-icon-button__ripple"></div>
                        notifications
                    </button>
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" aria-label="User">
                        <div class="mdc-icon-button__ripple"></div>
                        account_circle
                    </button>
                </section>
            </div>
        </header>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Sidebar Backdrop -->
            <div class="sidebar-backdrop" id="sidebar-backdrop"></div>
            <!-- Sidebar -->
            <aside class="sidebar closed" id="sidebar">
                <div class="mdc-drawer__header">
                    <h3 class="mdc-drawer__title">POS Menu</h3>
                    <h6 class="mdc-drawer__subtitle">Navigation</h6>
                </div>
                <div class="mdc-drawer__content">
                    <nav class="mdc-list">
                        <a class="mdc-list-item" href="index.html">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">dashboard</span>
                            <span class="mdc-list-item__text">Dashboard</span>
                        </a>
                        <a class="mdc-list-item" href="#">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">shopping_cart</span>
                            <span class="mdc-list-item__text">New Sale</span>
                        </a>
                        <a class="mdc-list-item" href="#">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">inventory</span>
                            <span class="mdc-list-item__text">Products</span>
                        </a>
                        <a class="mdc-list-item" href="#">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">people</span>
                            <span class="mdc-list-item__text">Customers</span>
                        </a>
                        <a class="mdc-list-item mdc-list-item--activated" href="data-table.html" aria-current="page">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">receipt_long</span>
                            <span class="mdc-list-item__text">Transactions</span>
                        </a>
                        <!-- Analytics with nested subitems -->
                        <div class="mdc-list-item mdc-list-item--collapsible">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">analytics</span>
                            <span class="mdc-list-item__text">Analytics</span>
                            <button class="mdc-icon-button mdc-list-item__meta" aria-label="Expand Analytics menu">
                                <div class="mdc-icon-button__ripple"></div>
                                <span class="material-icons">expand_more</span>
                            </button>
                        </div>
                        <div class="mdc-list-group mdc-list-group--hidden">
                            <a class="mdc-list-item mdc-list-item--nested" href="#">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic">bar_chart</span>
                                <span class="mdc-list-item__text">Sales Analytics</span>
                            </a>
                            <a class="mdc-list-item mdc-list-item--nested" href="#">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic">trending_up</span>
                                <span class="mdc-list-item__text">Growth Metrics</span>
                            </a>
                            <a class="mdc-list-item mdc-list-item--nested" href="#">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic">people_outline</span>
                                <span class="mdc-list-item__text">Customer Analytics</span>
                            </a>
                        </div>

                        <a class="mdc-list-item" href="#">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">bar_chart</span>
                            <span class="mdc-list-item__text">Reports</span>
                        </a>
                        <a class="mdc-list-item" href="#">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">settings</span>
                            <span class="mdc-list-item__text">Settings</span>
                        </a>
                    </nav>
                </div>
            </aside>

            <!-- Dashboard Content -->
            <main class="dashboard-content">
                <div class="page-content">
                    <h1>System Signals</h1>

                    <!-- Enhanced Material Design Data Table -->
                    <div class="mdc-card data-table-card mdc-card--outlined">
                        <!-- Data Table Header with Actions -->
                        <div class="data-table-header">
                            <!-- Filter Toolbar -->
                            <div class="filter-toolbar">
                                <button class="mdc-icon-button" aria-label="Filter">
                                    <div class="mdc-icon-button__ripple"></div>
                                    <span class="material-icons">filter_list</span>
                                </button>

                                <!-- Filter Chips -->
                                <div class="mdc-chip-set" role="grid">
                                    <div class="mdc-chip" role="row">
                                        <div class="mdc-chip__ripple"></div>
                                        <span class="mdc-chip__icon mdc-chip__icon--leading material-icons">check_circle</span>
                                        <span class="mdc-chip__text">Offline</span>
                                        <button class="mdc-chip__icon mdc-chip__icon--trailing material-icons" tabindex="-1">cancel</button>
                                    </div>
                                    <div class="mdc-chip" role="row">
                                        <div class="mdc-chip__ripple"></div>
                                        <span class="mdc-chip__icon mdc-chip__icon--leading material-icons">schedule</span>
                                        <span class="mdc-chip__text">Time ≥ 10min</span>
                                        <button class="mdc-chip__icon mdc-chip__icon--trailing material-icons" tabindex="-1">cancel</button>
                                    </div>
                                </div>
                            </div>

                            <!-- Data Table Actions -->
                            <div class="data-table-actions">
                                <!-- Search Field -->
                                <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--with-leading-icon search-field">
                                    <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">search</span>
                                    <input id="data-table-search" class="mdc-text-field__input" type="text" placeholder="Search signals...">
                                    <div class="mdc-notched-outline">
                                        <div class="mdc-notched-outline__leading"></div>
                                        <div class="mdc-notched-outline__notch">
                                            <label class="mdc-floating-label">Search</label>
                                        </div>
                                        <div class="mdc-notched-outline__trailing"></div>
                                    </div>
                                </div>

                                <!-- More Options Button -->
                                <button class="mdc-icon-button" aria-label="More options">
                                    <div class="mdc-icon-button__ripple"></div>
                                    <span class="material-icons">more_vert</span>
                                </button>
                            </div>
                        </div>

                        <!-- Data Table -->
                        <div class="mdc-data-table">
                            <div class="mdc-data-table__table-container">
                                <table class="mdc-data-table__table">
                                    <thead>
                                        <tr class="mdc-data-table__header-row">
                                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--checkbox">
                                                <div class="mdc-checkbox mdc-checkbox--selected mdc-data-table__header-row-checkbox">
                                                    <input type="checkbox" class="mdc-checkbox__native-control" aria-label="Toggle all rows"/>
                                                    <div class="mdc-checkbox__background">
                                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                                        </svg>
                                                        <div class="mdc-checkbox__mixedmark"></div>
                                                    </div>
                                                </div>
                                            </th>
                                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort" role="columnheader" scope="col" aria-sort="none">
                                                <div class="mdc-data-table__header-cell-wrapper">
                                                    <div class="mdc-data-table__header-cell-label">Status</div>
                                                    <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" aria-label="Sort by Status">arrow_upward</button>
                                                </div>
                                            </th>
                                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort" role="columnheader" scope="col" aria-sort="none" style="width: 200px">
                                                <div class="mdc-data-table__header-cell-wrapper">
                                                    <div class="mdc-data-table__header-cell-label">Signal Name</div>
                                                    <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" aria-label="Sort by Signal Name">arrow_upward</button>
                                                </div>
                                            </th>
                                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort" role="columnheader" scope="col" aria-sort="none">
                                                <div class="mdc-data-table__header-cell-wrapper">
                                                    <div class="mdc-data-table__header-cell-label">Severity</div>
                                                    <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" aria-label="Sort by Severity">arrow_upward</button>
                                                </div>
                                            </th>
                                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort" role="columnheader" scope="col" aria-sort="none">
                                                <div class="mdc-data-table__header-cell-wrapper">
                                                    <div class="mdc-data-table__header-cell-label">Stage</div>
                                                    <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" aria-label="Sort by Stage">arrow_upward</button>
                                                </div>
                                            </th>
                                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort" role="columnheader" scope="col" aria-sort="none">
                                                <div class="mdc-data-table__header-cell-wrapper">
                                                    <div class="mdc-data-table__header-cell-label">Lapsed Time</div>
                                                    <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" aria-label="Sort by Lapsed Time">arrow_upward</button>
                                                </div>
                                            </th>
                                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort" role="columnheader" scope="col" aria-sort="none">
                                                <div class="mdc-data-table__header-cell-wrapper">
                                                    <div class="mdc-data-table__header-cell-label">Team Lead</div>
                                                    <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" aria-label="Sort by Team Lead">arrow_upward</button>
                                                </div>
                                            </th>
                                            <th class="mdc-data-table__header-cell">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="mdc-data-table__content">
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                                <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                                    <input type="checkbox" class="mdc-checkbox__native-control" aria-labelledby="u0"/>
                                                    <div class="mdc-checkbox__background">
                                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                                        </svg>
                                                        <div class="mdc-checkbox__mixedmark"></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="mdc-data-table__cell">Offline</td>
                                            <td class="mdc-data-table__cell">Astrid: NE shared managed-features</td>
                                            <td class="mdc-data-table__cell"><span class="severity-medium">Medium</span></td>
                                            <td class="mdc-data-table__cell">Triaged</td>
                                            <td class="mdc-data-table__cell">10:12</td>
                                            <td class="mdc-data-table__cell">Chase Nguyen</td>
                                            <td class="mdc-data-table__cell">
                                                <div class="mdc-data-table__row-actions">
                                                    <button class="mdc-icon-button" aria-label="Edit">
                                                        <div class="mdc-icon-button__ripple"></div><span class="material-icons">edit</span>
                                                    </button>
                                                    <button class="mdc-icon-button" aria-label="Delete">
                                                        <div class="mdc-icon-button__ripple"></div><span class="material-icons">delete</span>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                                <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                                    <input type="checkbox" class="mdc-checkbox__native-control" aria-labelledby="u1"/>
                                                    <div class="mdc-checkbox__background">
                                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                                        </svg>
                                                        <div class="mdc-checkbox__mixedmark"></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="mdc-data-table__cell">Offline</td>
                                            <td class="mdc-data-table__cell">Cosmo: prod shared vm</td>
                                            <td class="mdc-data-table__cell"><span class="severity-huge">Huge</span></td>
                                            <td class="mdc-data-table__cell">Triaged</td>
                                            <td class="mdc-data-table__cell">12:45</td>
                                            <td class="mdc-data-table__cell">Brie Furman</td>
                                            <td class="mdc-data-table__cell">
                                                <div class="mdc-data-table__row-actions">
                                                    <button class="mdc-icon-button" aria-label="Edit">
                                                        <div class="mdc-icon-button__ripple"></div><span class="material-icons">edit</span>
                                                    </button>
                                                    <button class="mdc-icon-button" aria-label="Delete">
                                                        <div class="mdc-icon-button__ripple"></div><span class="material-icons">delete</span>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                                <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                                    <input type="checkbox" class="mdc-checkbox__native-control" aria-labelledby="u2"/>
                                                    <div class="mdc-checkbox__background">
                                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                                        </svg>
                                                        <div class="mdc-checkbox__mixedmark"></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="mdc-data-table__cell">Offline</td>
                                            <td class="mdc-data-table__cell">Phoenix: prod shared lyra-managed-features</td>
                                            <td class="mdc-data-table__cell"><span class="severity-minor">Minor</span></td>
                                            <td class="mdc-data-table__cell">Triaged</td>
                                            <td class="mdc-data-table__cell">13:06</td>
                                            <td class="mdc-data-table__cell">Jeremy Lake</td>
                                            <td class="mdc-data-table__cell">
                                                <div class="mdc-data-table__row-actions">
                                                    <button class="mdc-icon-button" aria-label="Edit">
                                                        <div class="mdc-icon-button__ripple"></div><span class="material-icons">edit</span>
                                                    </button>
                                                    <button class="mdc-icon-button" aria-label="Delete">
                                                        <div class="mdc-icon-button__ripple"></div><span class="material-icons">delete</span>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                                <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                                    <input type="checkbox" class="mdc-checkbox__native-control" aria-labelledby="u3"/>
                                                    <div class="mdc-checkbox__background">
                                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                                        </svg>
                                                        <div class="mdc-checkbox__mixedmark"></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="mdc-data-table__cell">Offline</td>
                                            <td class="mdc-data-table__cell">Sirius: prod shared ares-managed-vm</td>
                                            <td class="mdc-data-table__cell"><span class="severity-negligible">Negligible</span></td>
                                            <td class="mdc-data-table__cell">Triaged</td>
                                            <td class="mdc-data-table__cell">13:18</td>
                                            <td class="mdc-data-table__cell">Angelica Howards</td>
                                            <td class="mdc-data-table__cell">
                                                <div class="mdc-data-table__row-actions">
                                                    <button class="mdc-icon-button" aria-label="Edit">
                                                        <div class="mdc-icon-button__ripple"></div><span class="material-icons">edit</span>
                                                    </button>
                                                    <button class="mdc-icon-button" aria-label="Delete">
                                                        <div class="mdc-icon-button__ripple"></div><span class="material-icons">delete</span>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Data Table Pagination -->
                            <div class="mdc-data-table__pagination">
                                <div class="mdc-data-table__pagination-trailing">
                                    <div class="mdc-data-table__pagination-rows-per-page">
                                        <div class="mdc-data-table__pagination-rows-per-page-label">
                                            Rows per page
                                        </div>

                                        <div class="mdc-select mdc-select--outlined mdc-select--no-label mdc-data-table__pagination-rows-per-page-select">
                                            <div class="mdc-select__anchor" role="button" aria-haspopup="listbox">
                                                <span class="mdc-select__selected-text">4</span>
                                                <span class="mdc-select__dropdown-icon">
                                                    <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                                        <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                                        <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                                    </svg>
                                                </span>
                                                <span class="mdc-notched-outline">
                                                    <span class="mdc-notched-outline__leading"></span>
                                                    <span class="mdc-notched-outline__trailing"></span>
                                                </span>
                                            </div>

                                            <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                                                <ul class="mdc-list" role="listbox">
                                                    <li class="mdc-list-item mdc-list-item--selected" aria-selected="true" role="option" data-value="4">
                                                        <span class="mdc-list-item__text">4</span>
                                                    </li>
                                                    <li class="mdc-list-item" role="option" data-value="8">
                                                        <span class="mdc-list-item__text">8</span>
                                                    </li>
                                                    <li class="mdc-list-item" role="option" data-value="12">
                                                        <span class="mdc-list-item__text">12</span>
                                                    </li>
                                                    <li class="mdc-list-item" role="option" data-value="16">
                                                        <span class="mdc-list-item__text">16</span>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mdc-data-table__pagination-navigation">
                                        <div class="mdc-data-table__pagination-total">
                                            1-4 of 100
                                        </div>
                                        <button class="mdc-icon-button material-icons mdc-data-table__pagination-button" data-page="first" disabled>
                                            <div class="mdc-button__icon">first_page</div>
                                        </button>
                                        <button class="mdc-icon-button material-icons mdc-data-table__pagination-button" data-page="prev" disabled>
                                            <div class="mdc-button__icon">chevron_left</div>
                                        </button>
                                        <button class="mdc-icon-button material-icons mdc-data-table__pagination-button" data-page="next">
                                            <div class="mdc-button__icon">chevron_right</div>
                                        </button>
                                        <button class="mdc-icon-button material-icons mdc-data-table__pagination-button" data-page="last">
                                            <div class="mdc-button__icon">last_page</div>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- User Profile Offcanvas (Custom Implementation) -->
    <div class="user-drawer-container" id="user-drawer-container">
        <div class="user-drawer-backdrop" id="user-drawer-backdrop"></div>
        <div class="user-drawer" id="user-drawer">
            <div class="user-drawer-header">
                <button class="material-icons mdc-icon-button close-drawer-button" id="close-user-drawer">
                    <div class="mdc-icon-button__ripple"></div>
                    close
                </button>
                <div class="user-profile-header">
                    <div class="user-avatar">
                        <span class="material-icons">account_circle</span>
                    </div>
                    <h3 class="user-drawer-title">John Doe</h3>
                    <h6 class="user-drawer-subtitle"><EMAIL></h6>
                </div>
            </div>
            <div class="user-drawer-content">
                <nav class="mdc-list">
                    <a class="mdc-list-item" href="#" style="--item-index: 0;">
                        <span class="mdc-list-item__ripple"></span>
                        <span class="material-icons mdc-list-item__graphic">person</span>
                        <span class="mdc-list-item__text">My Profile</span>
                    </a>
                    <a class="mdc-list-item" href="#" style="--item-index: 1;">
                        <span class="mdc-list-item__ripple"></span>
                        <span class="material-icons mdc-list-item__graphic">settings</span>
                        <span class="mdc-list-item__text">Settings</span>
                    </a>
                    <a class="mdc-list-item" href="#" style="--item-index: 2;">
                        <span class="mdc-list-item__ripple"></span>
                        <span class="material-icons mdc-list-item__graphic">help</span>
                        <span class="mdc-list-item__text">Help & Support</span>
                    </a>
                    <hr class="mdc-list-divider">
                    <a class="mdc-list-item" href="#" style="--item-index: 3;">
                        <span class="mdc-list-item__ripple"></span>
                        <span class="material-icons mdc-list-item__graphic">logout</span>
                        <span class="mdc-list-item__text">Sign Out</span>
                    </a>
                </nav>
            </div>
        </div>
    </div>

    <!-- Bottom App Bar for Mobile -->
    <div class="bottom-app-bar" id="bottom-app-bar">
        <div class="bottom-app-bar-inner">
            <button class="bottom-nav-item">
                <div class="bottom-nav-ripple-container">
                    <div class="bottom-nav-ripple"></div>
                </div>
                <span class="material-icons">dashboard</span>
                <span class="bottom-nav-label">Dashboard</span>
            </button>
            <button class="bottom-nav-item active">
                <div class="bottom-nav-ripple-container">
                    <div class="bottom-nav-ripple"></div>
                </div>
                <span class="material-icons">shopping_cart</span>
                <span class="bottom-nav-label">Orders</span>
            </button>
            <button class="bottom-nav-item">
                <div class="bottom-nav-ripple-container">
                    <div class="bottom-nav-ripple"></div>
                </div>
                <span class="material-icons">inventory</span>
                <span class="bottom-nav-label">Products</span>
            </button>
            <button class="bottom-nav-item">
                <div class="bottom-nav-ripple-container">
                    <div class="bottom-nav-ripple"></div>
                </div>
                <span class="material-icons">people</span>
                <span class="bottom-nav-label">Customers</span>
            </button>
            <button class="bottom-nav-item">
                <div class="bottom-nav-ripple-container">
                    <div class="bottom-nav-ripple"></div>
                </div>
                <span class="material-icons">more_horiz</span>
                <span class="bottom-nav-label">More</span>
            </button>
        </div>
    </div>

    <!-- Bottom Actions Bar (for data-table row actions) -->
    <div class="bottom-actions-bar" id="bottom-actions-bar">
        <div class="bottom-actions-bar__inner">
            <div class="bottom-actions-bar__left">
                <div class="mdc-select mdc-select--filled mdc-select--no-label actions-select" id="actions-select">
                    <div class="mdc-select__anchor" role="button" aria-haspopup="listbox">
                        <span class="mdc-select__ripple"></span>
                        <span class="mdc-select__selected-text">Actions</span>
                        <span class="mdc-select__dropdown-icon">
                            <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                            </svg>
                        </span>
                        <div class="mdc-line-ripple"></div>
                    </div>
                    <div class="mdc-select__menu mdc-menu mdc-menu-surface">
                        <ul class="mdc-list" role="listbox">
                            <li class="mdc-list-item mdc-list-item--selected" role="option" data-value=""><span class="mdc-list-item__text">Actions</span></li>
                            <li class="mdc-list-item" role="option" data-value="pin"><span class="mdc-list-item__text">Pin</span></li>
                            <li class="mdc-list-item" role="option" data-value="archive"><span class="mdc-list-item__text">Archive</span></li>
                            <li class="mdc-list-item" role="option" data-value="delete"><span class="mdc-list-item__text">Delete</span></li>
                        </ul>
                    </div>
                </div>
                <button class="mdc-button mdc-button--raised" id="action-go">
                    <div class="mdc-button__ripple"></div>
                    <span class="mdc-button__label">Go</span>
                </button>
            </div>
            <button class="close-actions" id="close-actions" aria-label="Close actions">
                <span class="material-icons">close</span>
            </button>
        </div>
    </div>

    <!-- Notifications Menu -->
    <div class="mdc-menu-surface--anchor notifications-anchor">
        <div class="mdc-menu mdc-menu-surface notifications-menu" id="notifications-menu">
            <div class="notifications-header">
                <h3>Notifications</h3>
                <button class="mdc-button" id="mark-all-read">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Mark all as read</span>
                </button>
            </div>
            <ul class="mdc-list notifications-list">
                <li class="mdc-list-item notification-item unread">
                    <span class="mdc-list-item__ripple"></span>
                    <span class="material-icons notification-icon">shopping_cart</span>
                    <span class="mdc-list-item__text">
                        <span class="notification-title">New Order #1234</span>
                        <span class="notification-text">A new order has been placed</span>
                        <span class="notification-time">2 minutes ago</span>
                    </span>
                </li>
                <li class="mdc-list-item notification-item unread">
                    <span class="mdc-list-item__ripple"></span>
                    <span class="material-icons notification-icon">inventory</span>
                    <span class="mdc-list-item__text">
                        <span class="notification-title">Low Stock Alert</span>
                        <span class="notification-text">Product XYZ is running low</span>
                        <span class="notification-time">1 hour ago</span>
                    </span>
                </li>
                <li class="mdc-list-item notification-item">
                    <span class="mdc-list-item__ripple"></span>
                    <span class="material-icons notification-icon">person_add</span>
                    <span class="mdc-list-item__text">
                        <span class="notification-title">New Customer</span>
                        <span class="notification-text">Jane Smith registered as a new customer</span>
                        <span class="notification-time">Yesterday</span>
                    </span>
                </li>
                <li class="mdc-list-item notification-item"></li>
                    <span class="mdc-list-item__ripple"></span>
                    <span class="material-icons notification-icon">system_update</span>
                    <span class="mdc-list-item__text">
                        <span class="notification-title">System Update</span>
                        <span class="notification-text">System will be updated tonight</span>
                        <span class="notification-time">2 days ago</span>
                    </span>
                </li>
            </ul>
            <div class="notifications-footer">
                <a href="#" class="mdc-button">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">View All Notifications</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Material Components Web JavaScript -->
    <script src="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="js/script.js"></script>
    <script src="js/data-table-actions.js"></script>
</body>
</html>
