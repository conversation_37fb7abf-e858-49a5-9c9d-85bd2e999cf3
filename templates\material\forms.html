<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS System - Dashboard</title>

    <!-- Material Icons and Roboto Font -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Material Components Web CSS -->
    <link href="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <!-- Material Design Preloader -->
    <div class="preloader">
        <div class="spinner">
            <svg class="circular" viewBox="25 25 50 50">
                <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="3" stroke-miterlimit="10"/>
            </svg>
        </div>
    </div>

    <div class="app-container">
        <!-- Top App Bar -->
        <header class="mdc-top-app-bar app-bar">
            <div class="mdc-top-app-bar__row">
                <section class="mdc-top-app-bar__section mdc-top-app-bar__section--align-start">
                    <button class="material-icons mdc-top-app-bar__navigation-icon mdc-icon-button" id="menu-button" aria-label="Menu">
                        <div class="mdc-icon-button__ripple"></div>
                        menu
                    </button>
                    <span class="mdc-top-app-bar__title">POS System</span>
                </section>
                <section class="mdc-top-app-bar__section mdc-top-app-bar__section--align-end">
                    <!-- Desktop Search Bar (visible on larger screens) -->
                    <div class="search-container desktop-search">
                        <div class="mdc-text-field mdc-text-field--filled mdc-text-field--with-leading-icon mdc-text-field--no-label search-field">
                            <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">search</span>
                            <input class="mdc-text-field__input" type="text" placeholder="Search...">
                            <span class="mdc-line-ripple"></span>
                        </div>
                    </div>

                    <!-- Mobile Search Icon (visible on smaller screens) -->
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button mobile-search-icon" id="mobile-search-button" aria-label="Search">
                        <div class="mdc-icon-button__ripple"></div>
                        search
                    </button>

                    <!-- Mobile Search Bar (hidden by default, shown when search icon is clicked) -->
                    <div class="mobile-search-container" id="mobile-search-container">
                        <div class="mobile-search-inner">
                            <span class="material-icons mobile-search-icon">search</span>
                            <input type="text" class="mobile-search-input" placeholder="Search...">
                            <button class="material-icons mdc-icon-button mobile-search-close" id="mobile-search-close">
                                <div class="mdc-icon-button__ripple"></div>
                                close
                            </button>
                        </div>
                    </div>
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" id="theme-toggle-button" aria-label="Toggle dark mode">
                        <div class="mdc-icon-button__ripple"></div>
                        light_mode
                    </button>
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" aria-label="Notifications" style="display: none;">
                        <div class="mdc-icon-button__ripple"></div>
                        notifications
                    </button>
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" aria-label="User">
                        <div class="mdc-icon-button__ripple"></div>
                        account_circle
                    </button>
                </section>
            </div>
        </header>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Sidebar Backdrop -->
            <div class="sidebar-backdrop" id="sidebar-backdrop"></div>
            <!-- Sidebar -->
            <aside class="sidebar closed" id="sidebar">
                <div class="mdc-drawer__header">
                    <h3 class="mdc-drawer__title">POS Menu</h3>
                    <h6 class="mdc-drawer__subtitle">Navigation</h6>
                </div>
                <div class="mdc-drawer__content">
                    <nav class="mdc-list">
                        <a class="mdc-list-item mdc-list-item--activated" href="#" aria-current="page">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">dashboard</span>
                            <span class="mdc-list-item__text">Dashboard</span>
                        </a>
                        <a class="mdc-list-item" href="#">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">shopping_cart</span>
                            <span class="mdc-list-item__text">New Sale</span>
                        </a>
                        <a class="mdc-list-item" href="#">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">inventory</span>
                            <span class="mdc-list-item__text">Products</span>
                        </a>
                        <a class="mdc-list-item" href="#">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">people</span>
                            <span class="mdc-list-item__text">Customers</span>
                        </a>
                        <a class="mdc-list-item" href="#">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">receipt_long</span>
                            <span class="mdc-list-item__text">Transactions</span>
                        </a>
                        <!-- Analytics with nested subitems -->
                        <div class="mdc-list-item mdc-list-item--collapsible">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">analytics</span>
                            <span class="mdc-list-item__text">Analytics</span>
                            <button class="mdc-icon-button mdc-list-item__meta" aria-label="Expand Analytics menu">
                                <div class="mdc-icon-button__ripple"></div>
                                <span class="material-icons">expand_more</span>
                            </button>
                        </div>
                        <div class="mdc-list-group mdc-list-group--hidden">
                            <a class="mdc-list-item mdc-list-item--nested" href="#">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic">bar_chart</span>
                                <span class="mdc-list-item__text">Sales Analytics</span>
                            </a>
                            <a class="mdc-list-item mdc-list-item--nested" href="#">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic">trending_up</span>
                                <span class="mdc-list-item__text">Growth Metrics</span>
                            </a>
                            <a class="mdc-list-item mdc-list-item--nested" href="#">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic">people_outline</span>
                                <span class="mdc-list-item__text">Customer Analytics</span>
                            </a>
                        </div>

                        <a class="mdc-list-item" href="#">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">bar_chart</span>
                            <span class="mdc-list-item__text">Reports</span>
                        </a>
                        <a class="mdc-list-item" href="#">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">settings</span>
                            <span class="mdc-list-item__text">Settings</span>
                        </a>
                    </nav>
                </div>
            </aside>

            <!-- Dashboard Content -->
            <main class="dashboard-content">
                <div class="page-content">
                    <h1>Material Design Forms</h1>

                    <!-- Form Components -->
                    <div class="mdc-card mdc-card--outlined" style="padding: 24px;">
                        <h2 class="mdc-typography--headline6">Text Fields</h2>

                        <!-- Filled Text Field -->
                        <div class="mdc-text-field mdc-text-field--filled" style="margin: 16px 0;">
                            <span class="mdc-text-field__ripple"></span>
                            <span class="mdc-floating-label" id="filled-label">Filled Text Field</span>
                            <input class="mdc-text-field__input" type="text" aria-labelledby="filled-label">
                            <span class="mdc-line-ripple"></span>
                        </div>

                        <!-- Outlined Text Field -->
                        <div class="mdc-text-field mdc-text-field--outlined" style="margin: 16px 0;">
                            <input class="mdc-text-field__input" type="text" id="outlined-text-field">
                            <span class="mdc-notched-outline">
                                <span class="mdc-notched-outline__leading"></span>
                                <span class="mdc-notched-outline__notch">
                                    <span class="mdc-floating-label" id="outlined-label">Outlined Text Field</span>
                                </span>
                                <span class="mdc-notched-outline__trailing"></span>
                            </span>
                        </div>

                        <!-- Text Field with Leading Icon -->
                        <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--with-leading-icon" style="margin: 16px 0;">
                            <i class="material-icons mdc-text-field__icon">search</i>
                            <input class="mdc-text-field__input" type="text" id="text-field-with-icon">
                            <span class="mdc-notched-outline">
                                <span class="mdc-notched-outline__leading"></span>
                                <span class="mdc-notched-outline__notch">
                                    <span class="mdc-floating-label">With Leading Icon</span>
                                </span>
                                <span class="mdc-notched-outline__trailing"></span>
                            </span>
                        </div>

                        <!-- Text Field with Helper Text -->
                        <div style="margin: 16px 0;">
                            <div class="mdc-text-field mdc-text-field--outlined">
                                <input class="mdc-text-field__input" type="text" id="text-field-with-helper"
                                    aria-controls="helper-text" aria-describedby="helper-text">
                                <span class="mdc-notched-outline">
                                    <span class="mdc-notched-outline__leading"></span>
                                    <span class="mdc-notched-outline__notch">
                                        <span class="mdc-floating-label">With Helper Text</span>
                                    </span>
                                    <span class="mdc-notched-outline__trailing"></span>
                                </span>
                            </div>
                            <div class="mdc-text-field-helper-line">
                                <div class="mdc-text-field-helper-text" id="helper-text">Helper text</div>
                            </div>
                        </div>

                        <!-- Textarea -->
                        <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--textarea" style="margin: 16px 0;">
                            <span class="mdc-notched-outline">
                                <span class="mdc-notched-outline__leading"></span>
                                <span class="mdc-notched-outline__notch">
                                    <span class="mdc-floating-label">Textarea</span>
                                </span>
                                <span class="mdc-notched-outline__trailing"></span>
                            </span>
                            <textarea class="mdc-text-field__input" rows="8" cols="40"></textarea>
                        </div>
                    </div>

                    <!-- Selection Controls -->
                    <div class="mdc-card mdc-card--outlined" style="padding: 24px; margin-top: 24px;">
                        <h2 class="mdc-typography--headline6">Selection Controls</h2>

                        <!-- Checkbox -->
                        <div style="margin: 16px 0;">
                            <div class="mdc-form-field">
                                <div class="mdc-checkbox">
                                    <input type="checkbox" class="mdc-checkbox__native-control" id="checkbox"/>
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                    <div class="mdc-checkbox__ripple"></div>
                                </div>
                                <label for="checkbox">Checkbox</label>
                            </div>
                        </div>

                        <!-- Switch -->
                        <div style="margin: 16px 0;">
                            <div class="mdc-switch">
                                <div class="mdc-switch__track"></div>
                                <div class="mdc-switch__thumb-underlay">
                                    <div class="mdc-switch__thumb">
                                        <input type="checkbox" id="basic-switch" class="mdc-switch__native-control" role="switch" />
                                    </div>
                                    <div class="mdc-switch__ripple"></div>
                                </div>
                            </div>
                            <label for="basic-switch" style="margin-left: 10px;">Switch</label>
                        </div>
                    </div>

                    <!-- Select Menu -->
                    <div class="mdc-card mdc-card--outlined" style="padding: 24px; margin-top: 24px;">
                        <h2 class="mdc-typography--headline6">Select Menus</h2>

                        <!-- Filled Select -->
                        <div class="mdc-select mdc-select--filled" style="margin: 16px 0;">
                            <div class="mdc-select__anchor">
                                <span class="mdc-select__ripple"></span>
                                <span class="mdc-select__selected-text"></span>
                                <span class="mdc-select__dropdown-icon">
                                    <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                        <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                        <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                    </svg>
                                </span>
                                <span class="mdc-floating-label">Filled Select</span>
                                <span class="mdc-line-ripple"></span>
                            </div>
                            <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                                <ul class="mdc-list">
                                    <li class="mdc-list-item" data-value="1">
                                        <span class="mdc-list-item__ripple"></span>
                                        <span class="mdc-list-item__text">Option 1</span>
                                    </li>
                                    <li class="mdc-list-item" data-value="2">
                                        <span class="mdc-list-item__ripple"></span>
                                        <span class="mdc-list-item__text">Option 2</span>
                                    </li>
                                    <li class="mdc-list-item" data-value="3">
                                        <span class="mdc-list-item__ripple"></span>
                                        <span class="mdc-list-item__text">Option 3</span>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <!-- Outlined Select -->
                        <div class="mdc-select mdc-select--outlined" style="margin: 16px 0;">
                            <div class="mdc-select__anchor">
                                <span class="mdc-notched-outline">
                                    <span class="mdc-notched-outline__leading"></span>
                                    <span class="mdc-notched-outline__notch">
                                        <span class="mdc-floating-label">Outlined Select</span>
                                    </span>
                                    <span class="mdc-notched-outline__trailing"></span>
                                </span>
                                <span class="mdc-select__selected-text"></span>
                                <span class="mdc-select__dropdown-icon">
                                    <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                        <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                        <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                    </svg>
                                </span>
                            </div>
                            <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                                <ul class="mdc-list">
                                    <li class="mdc-list-item" data-value="1">
                                        <span class="mdc-list-item__ripple"></span>
                                        <span class="mdc-list-item__text">Option 1</span>
                                    </li>
                                    <li class="mdc-list-item" data-value="2">
                                        <span class="mdc-list-item__ripple"></span>
                                        <span class="mdc-list-item__text">Option 2</span>
                                    </li>
                                    <li class="mdc-list-item" data-value="3">
                                        <span class="mdc-list-item__ripple"></span>
                                        <span class="mdc-list-item__text">Option 3</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Buttons -->
                    <div class="mdc-card mdc-card--outlined" style="padding: 24px; margin-top: 24px;">
                        <h2 class="mdc-typography--headline6">Buttons</h2>

                        <div style="display: flex; gap: 16px; margin: 16px 0; flex-wrap: wrap;">
                            <!-- Text Button -->
                            <button class="mdc-button">
                                <span class="mdc-button__ripple"></span>
                                <span class="mdc-button__label">Text</span>
                            </button>

                            <!-- Raised Button -->
                            <button class="mdc-button mdc-button--raised">
                                <span class="mdc-button__ripple"></span>
                                <span class="mdc-button__label">Raised</span>
                            </button>

                            <!-- Outlined Button -->
                            <button class="mdc-button mdc-button--outlined">
                                <span class="mdc-button__ripple"></span>
                                <span class="mdc-button__label">Outlined</span>
                            </button>

                            <!-- Button with Icon -->
                            <button class="mdc-button mdc-button--raised">
                                <span class="mdc-button__ripple"></span>
                                <i class="material-icons mdc-button__icon">favorite</i>
                                <span class="mdc-button__label">With Icon</span>
                            </button>
                        </div>

                        <!-- FAB -->
                        <div style="margin: 16px 0;">
                            <button class="mdc-fab" aria-label="Favorite">
                                <div class="mdc-fab__ripple"></div>
                                <span class="material-icons mdc-fab__icon">favorite</span>
                            </button>

                            <button class="mdc-fab mdc-fab--extended" style="margin-left: 16px;">
                                <div class="mdc-fab__ripple"></div>
                                <span class="material-icons mdc-fab__icon">add</span>
                                <span class="mdc-fab__label">Extended FAB</span>
                            </button>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="mdc-card mdc-card--outlined" style="padding: 24px; margin-top: 24px; margin-bottom: 24px;">
                        <h2 class="mdc-typography--headline6">Form Actions</h2>
                        <div style="display: flex; justify-content: flex-end; gap: 8px; margin-top: 16px;">
                            <button class="mdc-button mdc-button--outlined">
                                <span class="mdc-button__ripple"></span>
                                <span class="mdc-button__label">Cancel</span>
                            </button>
                            <button class="mdc-button mdc-button--raised">
                                <span class="mdc-button__ripple"></span>
                                <span class="mdc-button__label">Save</span>
                            </button>
                        </div>
                    </div>

                    <!-- Banners -->
                    <div class="mdc-card mdc-card--outlined" style="padding: 24px; margin-top: 24px;">
                        <h2 class="mdc-typography--headline6">Banners</h2>
                        <p class="mdc-typography--body1" style="margin-top: 8px;">Banners display a prominent message and related optional actions.</p>
                        <div style="margin-top: 16px;">
                            <button class="mdc-button mdc-button--raised" id="show-banner-button">
                                <span class="mdc-button__ripple"></span>
                                <span class="mdc-button__label">Show Banner</span>
                            </button>
                        </div>
                        <!-- Banner Structure (hidden by default) -->
                        <div class="mdc-banner" role="banner" id="app-banner" style="display: none;">
                            <div class="mdc-banner__content" role="alertdialog" aria-live="assertive">
                                <div class="mdc-banner__graphic-text-wrapper">
                                    <div class="mdc-banner__graphic" role="img" alt="">
                                        <i class="material-icons mdc-banner__icon">error_outline</i>
                                    </div>
                                    <div class="mdc-banner__text">
                                        There was an error processing your request. Please try again later.
                                    </div>
                                </div>
                                <div class="mdc-banner__actions">
                                    <button type="button" class="mdc-button mdc-banner__secondary-action">
                                        <div class="mdc-button__ripple"></div>
                                        <span class="mdc-button__label">Learn More</span>
                                    </button>
                                    <button type="button" class="mdc-button mdc-banner__primary-action" id="close-banner-button">
                                        <div class="mdc-button__ripple"></div>
                                        <span class="mdc-button__label">Dismiss</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Dialog Form Template -->
                    <div class="mdc-card mdc-card--outlined" style="padding: 24px; margin-top: 24px;">
                        <h2 class="mdc-typography--headline6">Dialog Form Template</h2>
                        <p class="mdc-typography--body1" style="margin-top: 8px;">A full-page dialog form that takes up 75% of the page on medium and desktop screens.</p>
                        <div style="margin-top: 16px;">
                            <button class="mdc-button mdc-button--raised" id="show-dialog-form-button">
                                <span class="mdc-button__ripple"></span>
                                <span class="material-icons mdc-button__icon">open_in_new</span>
                                <span class="mdc-button__label">Open Dialog Form</span>
                            </button>
                        </div>
                    </div>

                    <!-- Snackbars -->
                    <div class="mdc-card mdc-card--outlined" style="padding: 24px; margin-top: 24px;">
                        <h2 class="mdc-typography--headline6">Snackbars</h2>
                        <p class="mdc-typography--body1" style="margin-top: 8px;">Snackbars provide brief messages about app processes at the bottom of the screen.</p>
                        <div style="display: flex; gap: 16px; margin-top: 16px; flex-wrap: wrap;">
                            <button class="mdc-button mdc-button--raised" id="show-snackbar-basic">
                                <span class="mdc-button__ripple"></span>
                                <span class="mdc-button__label">Basic Snackbar</span>
                            </button>
                            <button class="mdc-button mdc-button--raised" id="show-snackbar-action">
                                <span class="mdc-button__ripple"></span>
                                <span class="mdc-button__label">Snackbar with Action</span>
                            </button>
                             <button class="mdc-button mdc-button--raised" id="show-snackbar-leading">
                                <span class="mdc-button__ripple"></span>
                                <span class="mdc-button__label">Leading Snackbar</span>
                            </button>
                        </div>
                        <!-- Snackbar structure for examples (separate from the dialog one) -->
                        <div class="mdc-snackbar" id="example-snackbar">
                            <div class="mdc-snackbar__surface" role="status" aria-relevant="additions">
                                <div class="mdc-snackbar__label" aria-atomic="false"></div>
                                <div class="mdc-snackbar__actions" aria-atomic="true">
                                    <button type="button" class="mdc-button mdc-snackbar__action">
                                        <div class="mdc-button__ripple"></div>
                                        <span class="mdc-button__label"></span>
                                    </button>
                                    <button class="material-icons mdc-icon-button mdc-snackbar__dismiss" title="Dismiss">
                                        <div class="mdc-icon-button__ripple"></div>
                                        close
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tabs -->
                    <div class="mdc-card mdc-card--outlined" style="padding: 0; margin-top: 24px;">
                        <h2 class="mdc-typography--headline6" style="padding: 24px 24px 0 24px;">Tabs</h2>
                        <div class="mdc-tab-bar" role="tablist" id="content-tabs">
                            <div class="mdc-tab-scroller">
                                <div class="mdc-tab-scroller__scroll-area">
                                    <div class="mdc-tab-scroller__scroll-content">
                                        <button class="mdc-tab mdc-tab--active" role="tab" aria-selected="true" tabindex="0">
                                            <span class="mdc-tab__content">
                                                <span class="mdc-tab__icon material-icons" aria-hidden="true">home</span>
                                                <span class="mdc-tab__text-label">Home</span>
                                            </span>
                                            <span class="mdc-tab-indicator mdc-tab-indicator--active">
                                                <span class="mdc-tab-indicator__content mdc-tab-indicator__content--underline"></span>
                                            </span>
                                            <span class="mdc-tab__ripple"></span>
                                        </button>
                                        <button class="mdc-tab" role="tab" aria-selected="false" tabindex="-1">
                                            <span class="mdc-tab__content">
                                                <span class="mdc-tab__icon material-icons" aria-hidden="true">settings</span>
                                                <span class="mdc-tab__text-label">Settings</span>
                                            </span>
                                            <span class="mdc-tab-indicator">
                                                <span class="mdc-tab-indicator__content mdc-tab-indicator__content--underline"></span>
                                            </span>
                                            <span class="mdc-tab__ripple"></span>
                                        </button>
                                        <button class="mdc-tab" role="tab" aria-selected="false" tabindex="-1">
                                            <span class="mdc-tab__content">
                                                <span class="mdc-tab__icon material-icons" aria-hidden="true">info</span>
                                                <span class="mdc-tab__text-label">About</span>
                                            </span>
                                            <span class="mdc-tab-indicator">
                                                <span class="mdc-tab-indicator__content mdc-tab-indicator__content--underline"></span>
                                            </span>
                                            <span class="mdc-tab__ripple"></span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Tab Content Panels -->
                        <div class="tab-content" id="tab-content-home" style="padding: 24px;">
                            <p class="mdc-typography--body1">This is the content for the <strong>Home</strong> tab.</p>
                        </div>
                        <div class="tab-content" id="tab-content-settings" style="padding: 24px; display: none;">
                            <p class="mdc-typography--body1">This is the content for the <strong>Settings</strong> tab.</p>
                        </div>
                        <div class="tab-content" id="tab-content-about" style="padding: 24px; display: none;">
                            <p class="mdc-typography--body1">This is the content for the <strong>About</strong> tab.</p>
                        </div>
                    </div>

                    <!-- Dialogs Section -->
                    <div class="mdc-card component-section">
                        <h2>Dialogs</h2>
                        <button class="mdc-button mdc-button--raised" id="show-alert-dialog-button">
                            <span class="mdc-button__label">Show Alert Dialog</span>
                        </button>
                        <button class="mdc-button mdc-button--raised" id="show-confirmation-dialog-button">
                            <span class="mdc-button__label">Show Confirmation Dialog</span>
                        </button>
                        <button class="mdc-button mdc-button--raised" id="show-simple-dialog-button">
                            <span class="mdc-button__label">Show Simple Dialog</span>
                        </button>
                        <button class="mdc-button mdc-button--raised" id="show-registration-dialog-button">
                            <span class="mdc-button__label">Show Registration Dialog</span>
                        </button>
                        <button class="mdc-button mdc-button--raised" id="show-product-dialog-button">
                            <span class="mdc-button__label">Show Product Form</span>
                        </button>
                    </div>

                </div>
            </main>
        </div>
    </div>

    <!-- Alert Dialog -->
    <div class="mdc-dialog" id="alert-dialog">
        <div class="mdc-dialog__container">
            <div class="mdc-dialog__surface"
                role="alertdialog"
                aria-modal="true"
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-content">
                <h2 class="mdc-dialog__title" id="alert-dialog-title">Alert</h2>
                <div class="mdc-dialog__content" id="alert-dialog-content">
                    This is an important alert message!
                </div>
                <div class="mdc-dialog__actions">
                    <button type="button" class="mdc-button mdc-dialog__button" data-mdc-dialog-action="ok">
                        <div class="mdc-button__ripple"></div>
                        <span class="mdc-button__label">OK</span>
                    </button>
                </div>
            </div>
        </div>
        <div class="mdc-dialog__scrim"></div>
    </div>

    <!-- Confirmation Dialog -->
    <div class="mdc-dialog" id="confirmation-dialog">
        <div class="mdc-dialog__container">
            <div class="mdc-dialog__surface"
                role="dialog"
                aria-modal="true"
                aria-labelledby="confirmation-dialog-title"
                aria-describedby="confirmation-dialog-content">
                <h2 class="mdc-dialog__title" id="confirmation-dialog-title">Confirm Action</h2>
                <div class="mdc-dialog__content" id="confirmation-dialog-content">
                    Are you sure you want to proceed with this action?
                </div>
                <div class="mdc-dialog__actions">
                    <button type="button" class="mdc-button mdc-dialog__button" data-mdc-dialog-action="cancel">
                        <div class="mdc-button__ripple"></div>
                        <span class="mdc-button__label">Cancel</span>
                    </button>
                    <button type="button" class="mdc-button mdc-dialog__button" data-mdc-dialog-action="accept">
                        <div class="mdc-button__ripple"></div>
                        <span class="mdc-button__label">Accept</span>
                    </button>
                </div>
            </div>
        </div>
        <div class="mdc-dialog__scrim"></div>
    </div>

    <!-- Simple Dialog (List) -->
    <div class="mdc-dialog" id="simple-dialog">
        <div class="mdc-dialog__container">
            <div class="mdc-dialog__surface"
                role="dialog"
                aria-modal="true"
                aria-labelledby="simple-dialog-title">
                <h2 class="mdc-dialog__title" id="simple-dialog-title">Select Account</h2>
                <ul class="mdc-list" role="listbox" aria-orientation="vertical" id="simple-dialog-list">
                    <li class="mdc-list-item" role="option" tabindex="0" data-mdc-dialog-action="<EMAIL>">
                        <span class="mdc-list-item__ripple"></span>
                        <span class="mdc-list-item__text"><EMAIL></span>
                    </li>
                    <li class="mdc-list-item" role="option" tabindex="-1" data-mdc-dialog-action="<EMAIL>">
                        <span class="mdc-list-item__ripple"></span>
                        <span class="mdc-list-item__text"><EMAIL></span>
                    </li>
                    <li class="mdc-list-item" role="option" tabindex="-1" data-mdc-dialog-action="add_account">
                        <span class="mdc-list-item__ripple"></span>
                        <span class="mdc-list-item__graphic" aria-hidden="true">
                            <i class="material-icons">add</i>
                        </span>
                        <span class="mdc-list-item__text">Add account</span>
                    </li>
                </ul>
            </div>
        </div>
        <div class="mdc-dialog__scrim"></div>
    </div>

    <!-- Registration Full-Screen Dialog -->
    <div class="mdc-dialog mdc-dialog--fullscreen" id="registration-dialog">
        <div class="mdc-dialog__container">
            <div class="mdc-dialog__surface"
                role="dialog"
                aria-modal="true"
                aria-labelledby="registration-dialog-title">
                <h2 class="mdc-dialog__title" id="registration-dialog-title">Register New Account</h2>
                <div class="mdc-dialog__content">
                    <form id="registration-form">
                        <!-- Name -->
                        <div class="mdc-text-field mdc-text-field--filled" style="width:100%; margin-bottom:16px;">
                            <span class="mdc-text-field__ripple"></span>
                            <input type="text" class="mdc-text-field__input" id="reg-name" required aria-labelledby="reg-name-label">
                            <span class="mdc-floating-label" id="reg-name-label">Full Name</span>
                            <span class="mdc-line-ripple"></span>
                        </div>
                        <!-- Email -->
                        <div class="mdc-text-field mdc-text-field--filled" style="width:100%; margin-bottom:16px;">
                            <span class="mdc-text-field__ripple"></span>
                            <input type="email" class="mdc-text-field__input" id="reg-email" required aria-labelledby="reg-email-label">
                            <span class="mdc-floating-label" id="reg-email-label">Email Address</span>
                            <span class="mdc-line-ripple"></span>
                        </div>
                        <!-- Password -->
                        <div class="mdc-text-field mdc-text-field--filled" style="width:100%; margin-bottom:16px;">
                            <span class="mdc-text-field__ripple"></span>
                            <input type="password" class="mdc-text-field__input" id="reg-password" required aria-labelledby="reg-password-label">
                            <span class="mdc-floating-label" id="reg-password-label">Password</span>
                            <span class="mdc-line-ripple"></span>
                        </div>
                        <!-- Confirm Password -->
                        <div class="mdc-text-field mdc-text-field--filled" style="width:100%; margin-bottom:16px;">
                            <span class="mdc-text-field__ripple"></span>
                            <input type="password" class="mdc-text-field__input" id="reg-confirm-password" required aria-labelledby="reg-confirm-password-label">
                            <span class="mdc-floating-label" id="reg-confirm-password-label">Confirm Password</span>
                            <span class="mdc-line-ripple"></span>
                        </div>
                    </form>
                </div>
                <div class="mdc-dialog__actions">
                    <button type="button" class="mdc-button mdc-dialog__button" data-mdc-dialog-action="cancel">
                        <span class="mdc-button__label">Cancel</span>
                    </button>
                    <button type="button" class="mdc-button mdc-dialog__button" data-mdc-dialog-action="register">
                        <span class="mdc-button__label">Register</span>
                    </button>
                </div>
            </div>
        </div>
        <div class="mdc-dialog__scrim"></div>
    </div>

    <!-- Product Create Dialog -->
    <div class="mdc-dialog" id="product-dialog">
        <div class="mdc-dialog__container">
            <div class="mdc-dialog__surface" role="dialog" aria-modal="true" aria-labelledby="product-dialog-title">
                <button class="material-icons mdc-icon-button" data-mdc-dialog-action="cancel" aria-label="Close Product Dialog">
                    <div class="mdc-icon-button__ripple"></div>
                    close
                </button>
                <h2 class="mdc-dialog__title" id="product-dialog-title">Create Product</h2>
                <div class="mdc-dialog__content">
                    <form id="product-form">
                        <!-- Product Name -->
                        <div class="mdc-text-field mdc-text-field--outlined" style="width:100%; margin-bottom:16px;">
                            <input class="mdc-text-field__input" id="product-name" required>
                            <span class="mdc-notched-outline">
                                <span class="mdc-notched-outline__leading"></span>
                                <span class="mdc-notched-outline__notch">
                                    <span class="mdc-floating-label">Product Name</span>
                                </span>
                                <span class="mdc-notched-outline__trailing"></span>
                            </span>
                        </div>
                        <!-- Product Price -->
                        <div class="mdc-text-field mdc-text-field--outlined" style="width:100%; margin-bottom:16px;">
                            <input type="number" step="0.01" class="mdc-text-field__input" id="product-price" required>
                            <span class="mdc-notched-outline">
                                <span class="mdc-notched-outline__leading"></span>
                                <span class="mdc-notched-outline__notch">
                                    <span class="mdc-floating-label">Price</span>
                                </span>
                                <span class="mdc-notched-outline__trailing"></span>
                            </span>
                        </div>
                        <!-- Category Select -->
                        <div class="mdc-select mdc-select--outlined" style="width:100%; margin-bottom:16px;">
                            <div class="mdc-select__anchor">
                                <span class="mdc-notched-outline">
                                    <span class="mdc-notched-outline__leading"></span>
                                    <span class="mdc-notched-outline__notch">
                                        <span class="mdc-floating-label">Category</span>
                                    </span>
                                    <span class="mdc-notched-outline__trailing"></span>
                                </span>
                                <span class="mdc-select__selected-text"></span>
                                <span class="mdc-select__dropdown-icon">
                                    <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                        <polygon class="mdc-select__dropdown-icon-inactive" points="7 10 12 15 17 10"></polygon>
                                        <polygon class="mdc-select__dropdown-icon-active" points="7 15 12 10 17 15"></polygon>
                                    </svg>
                                </span>
                            </div>
                            <div class="mdc-select__menu mdc-menu mdc-menu-surface">
                                <ul class="mdc-list">
                                    <li class="mdc-list-item" data-value="electronics"><span class="mdc-list-item__ripple"></span><span class="mdc-list-item__text">Electronics</span></li>
                                    <li class="mdc-list-item" data-value="apparel"><span class="mdc-list-item__ripple"></span><span class="mdc-list-item__text">Apparel</span></li>
                                    <li class="mdc-list-item" data-value="accessories"><span class="mdc-list-item__ripple"></span><span class="mdc-list-item__text">Accessories</span></li>
                                </ul>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="mdc-dialog__actions">
                    <button type="button" class="mdc-button mdc-dialog__button" data-mdc-dialog-action="cancel">
                        <span class="mdc-button__label">Cancel</span>
                    </button>
                    <button type="button" class="mdc-button mdc-dialog__button" data-mdc-dialog-action="save">
                        <span class="mdc-button__label">Save</span>
                    </button>
                </div>
            </div>
        </div>
        <div class="mdc-dialog__scrim"></div>
    </div>

    <!-- User Profile Offcanvas (Custom Implementation) -->
    <div class="user-drawer-container" id="user-drawer-container">
        <div class="user-drawer-backdrop" id="user-drawer-backdrop"></div>
        <div class="user-drawer" id="user-drawer">
            <div class="user-drawer-header">
                <button class="material-icons mdc-icon-button close-drawer-button" id="close-user-drawer">
                    <div class="mdc-icon-button__ripple"></div>
                    close
                </button>
                <div class="user-profile-header">
                    <div class="user-avatar">
                        <span class="material-icons">account_circle</span>
                    </div>
                    <h3 class="user-drawer-title">John Doe</h3>
                    <h6 class="user-drawer-subtitle"><EMAIL></h6>
                </div>
            </div>
            <div class="user-drawer-content">
                <nav class="mdc-list">
                    <a class="mdc-list-item" href="#" style="--item-index: 0;">
                        <span class="mdc-list-item__ripple"></span>
                        <span class="material-icons mdc-list-item__graphic">person</span>
                        <span class="mdc-list-item__text">My Profile</span>
                    </a>
                    <a class="mdc-list-item" href="#" style="--item-index: 1;">
                        <span class="mdc-list-item__ripple"></span>
                        <span class="material-icons mdc-list-item__graphic">settings</span>
                        <span class="mdc-list-item__text">Settings</span>
                    </a>
                    <a class="mdc-list-item" href="#" style="--item-index: 2;">
                        <span class="mdc-list-item__ripple"></span>
                        <span class="material-icons mdc-list-item__graphic">help</span>
                        <span class="mdc-list-item__text">Help & Support</span>
                    </a>
                    <hr class="mdc-list-divider">
                    <a class="mdc-list-item" href="#" style="--item-index: 3;">
                        <span class="mdc-list-item__ripple"></span>
                        <span class="material-icons mdc-list-item__graphic">logout</span>
                        <span class="mdc-list-item__text">Sign Out</span>
                    </a>
                </nav>
            </div>
        </div>
    </div>

    <!-- Bottom App Bar for Mobile -->
    <div class="bottom-app-bar" id="bottom-app-bar">
        <div class="bottom-app-bar-inner">
            <button class="bottom-nav-item active">
                <div class="bottom-nav-ripple-container">
                    <div class="bottom-nav-ripple"></div>
                </div>
                <span class="material-icons">dashboard</span>
                <span class="bottom-nav-label">Dashboard</span>
            </button>
            <button class="bottom-nav-item">
                <div class="bottom-nav-ripple-container">
                    <div class="bottom-nav-ripple"></div>
                </div>
                <span class="material-icons">shopping_cart</span>
                <span class="bottom-nav-label">Orders</span>
            </button>
            <button class="bottom-nav-item">
                <div class="bottom-nav-ripple-container">
                    <div class="bottom-nav-ripple"></div>
                </div>
                <span class="material-icons">inventory</span>
                <span class="bottom-nav-label">Products</span>
            </button>
            <button class="bottom-nav-item">
                <div class="bottom-nav-ripple-container">
                    <div class="bottom-nav-ripple"></div>
                </div>
                <span class="material-icons">people</span>
                <span class="bottom-nav-label">Customers</span>
            </button>
            <button class="bottom-nav-item">
                <div class="bottom-nav-ripple-container">
                    <div class="bottom-nav-ripple"></div>
                </div>
                <span class="material-icons">more_horiz</span>
                <span class="bottom-nav-label">More</span>
            </button>
        </div>
    </div>

    <!-- Notifications Menu -->
    <div class="mdc-menu-surface--anchor notifications-anchor">
        <div class="mdc-menu mdc-menu-surface notifications-menu" id="notifications-menu">
            <div class="notifications-header">
                <h3>Notifications</h3>
                <button class="mdc-button" id="mark-all-read">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Mark all as read</span>
                </button>
            </div>
            <ul class="mdc-list notifications-list">
                <li class="mdc-list-item notification-item unread">
                    <span class="mdc-list-item__ripple"></span>
                    <span class="material-icons notification-icon">shopping_cart</span>
                    <span class="mdc-list-item__text">
                        <span class="notification-title">New Order #1234</span>
                        <span class="notification-text">A new order has been placed</span>
                        <span class="notification-time">2 minutes ago</span>
                    </span>
                </li>
                <li class="mdc-list-item notification-item unread">
                    <span class="mdc-list-item__ripple"></span>
                    <span class="material-icons notification-icon">inventory</span>
                    <span class="mdc-list-item__text">
                        <span class="notification-title">Low Stock Alert</span>
                        <span class="notification-text">Product XYZ is running low</span>
                        <span class="notification-time">1 hour ago</span>
                    </span>
                </li>
                <li class="mdc-list-item notification-item">
                    <span class="mdc-list-item__ripple"></span>
                    <span class="material-icons notification-icon">person_add</span>
                    <span class="mdc-list-item__text">
                        <span class="notification-title">New Customer</span>
                        <span class="notification-text">Jane Smith registered as a new customer</span>
                        <span class="notification-time">Yesterday</span>
                    </span>
                </li>
                <li class="mdc-list-item notification-item">
                    <span class="mdc-list-item__ripple"></span>
                    <span class="material-icons notification-icon">system_update</span>
                    <span class="mdc-list-item__text">
                        <span class="notification-title">System Update</span>
                        <span class="notification-text">System will be updated tonight</span>
                        <span class="notification-time">2 days ago</span>
                    </span>
                </li>
            </ul>
            <div class="notifications-footer">
                <a href="#" class="mdc-button">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">View All Notifications</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Modal Dialog Container -->
    <div class="modal-container" id="modal-container">
        <div class="modal-backdrop" id="modal-backdrop"></div>
        <div class="modal-dialog" id="modal-dialog">
            <div class="modal-header">
                <h2 class="mdc-typography--headline6">Add New User</h2>
                <button class="material-icons mdc-icon-button close-modal-button" id="close-modal-button">
                    <div class="mdc-icon-button__ripple"></div>
                    close
                </button>
            </div>
            <div class="modal-content">
                <form id="modal-form">
                    <!-- Name Field -->
                    <div class="mdc-text-field mdc-text-field--outlined" style="width: 100%; margin-bottom: 16px;">
                        <input class="mdc-text-field__input" type="text" id="modal-name" required>
                        <span class="mdc-notched-outline">
                            <span class="mdc-notched-outline__leading"></span>
                            <span class="mdc-notched-outline__notch">
                                <span class="mdc-floating-label" id="modal-name-label">Full Name</span>
                            </span>
                            <span class="mdc-notched-outline__trailing"></span>
                        </span>
                    </div>

                    <!-- Email Field -->
                    <div class="mdc-text-field mdc-text-field--outlined" style="width: 100%; margin-bottom: 16px;">
                        <input class="mdc-text-field__input" type="email" id="modal-email" required>
                        <span class="mdc-notched-outline">
                            <span class="mdc-notched-outline__leading"></span>
                            <span class="mdc-notched-outline__notch">
                                <span class="mdc-floating-label" id="modal-email-label">Email Address</span>
                            </span>
                            <span class="mdc-notched-outline__trailing"></span>
                        </span>
                    </div>

                    <!-- Role Select -->
                    <div class="mdc-select mdc-select--outlined" style="width: 100%; margin-bottom: 16px;">
                        <div class="mdc-select__anchor">
                            <span class="mdc-notched-outline">
                                <span class="mdc-notched-outline__leading"></span>
                                <span class="mdc-notched-outline__notch">
                                    <span class="mdc-floating-label">User Role</span>
                                </span>
                                <span class="mdc-notched-outline__trailing"></span>
                            </span>
                            <span class="mdc-select__selected-text"></span>
                            <span class="mdc-select__dropdown-icon">
                                <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                    <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                    <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                </svg>
                            </span>
                        </div>
                        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                            <ul class="mdc-list">
                                <li class="mdc-list-item" data-value="admin">
                                    <span class="mdc-list-item__ripple"></span>
                                    <span class="mdc-list-item__text">Administrator</span>
                                </li>
                                <li class="mdc-list-item" data-value="manager">
                                    <span class="mdc-list-item__ripple"></span>
                                    <span class="mdc-list-item__text">Manager</span>
                                </li>
                                <li class="mdc-list-item" data-value="user">
                                    <span class="mdc-list-item__ripple"></span>
                                    <span class="mdc-list-item__text">Standard User</span>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- Active Status Switch -->
                    <div style="display: flex; align-items: center; margin-bottom: 16px;">
                        <div class="mdc-switch">
                            <div class="mdc-switch__track"></div>
                            <div class="mdc-switch__thumb-underlay">
                                <div class="mdc-switch__thumb">
                                    <input type="checkbox" id="modal-active" class="mdc-switch__native-control" role="switch" checked />
                                </div>
                                <div class="mdc-switch__ripple"></div>
                            </div>
                        </div>
                        <label for="modal-active" style="margin-left: 10px;">Active Account</label>
                    </div>

                    <!-- Send Welcome Email Checkbox -->
                    <div class="mdc-form-field" style="margin-bottom: 24px;">
                        <div class="mdc-checkbox">
                            <input type="checkbox" class="mdc-checkbox__native-control" id="modal-welcome-email" checked/>
                            <div class="mdc-checkbox__background">
                                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                    <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                </svg>
                                <div class="mdc-checkbox__mixedmark"></div>
                            </div>
                            <div class="mdc-checkbox__ripple"></div>
                        </div>
                        <label for="modal-welcome-email">Send welcome email</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="mdc-button" id="modal-cancel-button">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Cancel</span>
                </button>
                <button class="mdc-button mdc-button--raised" id="modal-save-button">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Save</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Dialog Form Template Container -->
    <div class="dialog-form-container" id="dialog-form-container">
        <div class="dialog-form-backdrop" id="dialog-form-backdrop"></div>
        <div class="dialog-form" id="dialog-form">
            <!-- Dialog Form Header -->
            <div class="dialog-form-header">
                <h2 class="mdc-typography--headline6">Create New Product</h2>
                <button class="material-icons mdc-icon-button close-dialog-form-button" id="close-dialog-form-button">
                    <div class="mdc-icon-button__ripple"></div>
                    close
                </button>
            </div>

            <!-- Dialog Form Content -->
            <div class="dialog-form-content">
                <form id="dialog-product-form">
                    <!-- Product Name Field -->
                    <div class="mdc-text-field mdc-text-field--outlined" style="width: 100%; margin-bottom: 24px;">
                        <input class="mdc-text-field__input" type="text" id="dialog-product-name" required>
                        <span class="mdc-notched-outline">
                            <span class="mdc-notched-outline__leading"></span>
                            <span class="mdc-notched-outline__notch">
                                <span class="mdc-floating-label" id="dialog-product-name-label">Product Name</span>
                            </span>
                            <span class="mdc-notched-outline__trailing"></span>
                        </span>
                    </div>

                    <!-- Product Description Field -->
                    <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--textarea" style="width: 100%; margin-bottom: 24px;">
                        <span class="mdc-notched-outline">
                            <span class="mdc-notched-outline__leading"></span>
                            <span class="mdc-notched-outline__notch">
                                <span class="mdc-floating-label">Product Description</span>
                            </span>
                            <span class="mdc-notched-outline__trailing"></span>
                        </span>
                        <textarea class="mdc-text-field__input" rows="4" id="dialog-product-description"></textarea>
                    </div>

                    <!-- Two Column Layout for Price and Category -->
                    <div style="display: flex; gap: 24px; margin-bottom: 24px; flex-wrap: wrap;">
                        <!-- Price Field -->
                        <div class="mdc-text-field mdc-text-field--outlined" style="flex: 1; min-width: 200px;">
                            <input class="mdc-text-field__input" type="number" step="0.01" min="0" id="dialog-product-price" required>
                            <span class="mdc-notched-outline">
                                <span class="mdc-notched-outline__leading"></span>
                                <span class="mdc-notched-outline__notch">
                                    <span class="mdc-floating-label" id="dialog-product-price-label">Price ($)</span>
                                </span>
                                <span class="mdc-notched-outline__trailing"></span>
                            </span>
                        </div>

                        <!-- Category Select -->
                        <div class="mdc-select mdc-select--outlined" id="dialog-product-category" style="flex: 1; min-width: 200px;">
                            <div class="mdc-select__anchor">
                                <span class="mdc-notched-outline">
                                    <span class="mdc-notched-outline__leading"></span>
                                    <span class="mdc-notched-outline__notch">
                                        <span class="mdc-floating-label">Category</span>
                                    </span>
                                    <span class="mdc-notched-outline__trailing"></span>
                                </span>
                                <span class="mdc-select__selected-text"></span>
                                <span class="mdc-select__dropdown-icon">
                                    <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                        <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                        <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                    </svg>
                                </span>
                            </div>
                            <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                                <ul class="mdc-list">
                                    <li class="mdc-list-item" data-value="electronics">
                                        <span class="mdc-list-item__ripple"></span>
                                        <span class="mdc-list-item__text">Electronics</span>
                                    </li>
                                    <li class="mdc-list-item" data-value="clothing">
                                        <span class="mdc-list-item__ripple"></span>
                                        <span class="mdc-list-item__text">Clothing</span>
                                    </li>
                                    <li class="mdc-list-item" data-value="food">
                                        <span class="mdc-list-item__ripple"></span>
                                        <span class="mdc-list-item__text">Food & Beverages</span>
                                    </li>
                                    <li class="mdc-list-item" data-value="other">
                                        <span class="mdc-list-item__ripple"></span>
                                        <span class="mdc-list-item__text">Other</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Two Column Layout for Stock and Status -->
                    <div style="display: flex; gap: 24px; margin-bottom: 24px; flex-wrap: wrap;">
                        <!-- Stock Field -->
                        <div class="mdc-text-field mdc-text-field--outlined" style="flex: 1; min-width: 200px;">
                            <input class="mdc-text-field__input" type="number" min="0" id="dialog-product-stock" required>
                            <span class="mdc-notched-outline">
                                <span class="mdc-notched-outline__leading"></span>
                                <span class="mdc-notched-outline__notch">
                                    <span class="mdc-floating-label" id="dialog-product-stock-label">Stock Quantity</span>
                                </span>
                                <span class="mdc-notched-outline__trailing"></span>
                            </span>
                        </div>

                        <!-- Status Switch -->
                        <div style="flex: 1; min-width: 200px; display: flex; align-items: center;">
                            <div class="mdc-switch">
                                <div class="mdc-switch__track"></div>
                                <div class="mdc-switch__thumb-underlay">
                                    <div class="mdc-switch__thumb"></div>
                                    <input type="checkbox" id="dialog-product-active" class="mdc-switch__native-control" role="switch" checked>
                                    <div class="mdc-switch__ripple"></div>
                                </div>
                            </div>
                            <label for="dialog-product-active" style="margin-left: 10px;">Active Product</label>
                        </div>
                    </div>

                    <!-- Featured Product Checkbox -->
                    <div class="mdc-form-field" style="margin-bottom: 24px;">
                        <div class="mdc-checkbox">
                            <input type="checkbox" class="mdc-checkbox__native-control" id="dialog-product-featured"/>
                            <div class="mdc-checkbox__background">
                                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                    <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                </svg>
                                <div class="mdc-checkbox__mixedmark"></div>
                            </div>
                            <div class="mdc-checkbox__ripple"></div>
                        </div>
                        <label for="dialog-product-featured">Featured Product</label>
                    </div>

                    <!-- Tags Field -->
                    <div class="mdc-text-field mdc-text-field--outlined" style="width: 100%; margin-bottom: 24px;">
                        <input class="mdc-text-field__input" type="text" id="dialog-product-tags">
                        <span class="mdc-notched-outline">
                            <span class="mdc-notched-outline__leading"></span>
                            <span class="mdc-notched-outline__notch">
                                <span class="mdc-floating-label" id="dialog-product-tags-label">Tags (comma separated)</span>
                            </span>
                            <span class="mdc-notched-outline__trailing"></span>
                        </span>
                    </div>

                    <!-- Notes Field -->
                    <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--textarea" style="width: 100%;">
                        <span class="mdc-notched-outline">
                            <span class="mdc-notched-outline__leading"></span>
                            <span class="mdc-notched-outline__notch">
                                <span class="mdc-floating-label">Additional Notes</span>
                            </span>
                            <span class="mdc-notched-outline__trailing"></span>
                        </span>
                        <textarea class="mdc-text-field__input" rows="3" id="dialog-product-notes"></textarea>
                    </div>
                </form>
            </div>

            <!-- Dialog Form Footer -->
            <div class="dialog-form-footer">
                <button class="mdc-button mdc-button--outlined mdc-ripple-upgraded" id="dialog-form-cancel-button">
                    <span class="mdc-button__ripple"></span>
                    <i class="material-icons mdc-button__icon">close</i>
                    <span class="mdc-button__label">Cancel</span>
                </button>
                <button class="mdc-button mdc-button--raised" id="dialog-form-save-button">
                    <span class="mdc-button__ripple"></span>
                    <i class="material-icons mdc-button__icon">save</i>
                    <span class="mdc-button__label">Save Product</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Material Components Web JavaScript -->
    <script src="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="js/script.js"></script>
</body>
</html>
