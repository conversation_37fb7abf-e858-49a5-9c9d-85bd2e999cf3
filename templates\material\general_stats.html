{% load static %}
{% load humanize %}

{% block page_title %}{{ page_title }} | EcolePro{% endblock %}

{% block content %}
<div class="dashboard-header">
    <h1>Tableau de bord</h1>
    
    {% if remaining_days %}
    <div class="mdc-banner" role="banner">
        <div class="mdc-banner__content" role="alertdialog" aria-live="assertive">
            <div class="mdc-banner__graphic-text-wrapper">
                <div class="mdc-banner__graphic">
                    <span class="material-icons">warning</span>
                </div>
                <div class="mdc-banner__text">
                    Vous utilisez la version d'éssai de l'application EcolePro. Il vous reste {{ remaining_days }} jours d'utilisation gratuite.
                </div>
            </div>
            <div class="mdc-banner__actions">
                <button type="button" class="mdc-button mdc-banner__primary-action">
                    <div class="mdc-button__ripple"></div>
                    <div class="mdc-button__label">Compris</div>
                </button>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Call to Action Cards -->
{% if user.is_authenticated and perms.school.view_student and user.role != 'TC' %}
<div class="action-cards">
    {% if levels_count == 0 %}
    <div class="mdc-card action-card mdc-card--outlined">
        <div class="mdc-card__ripple"></div>
        <div class="card-header">
            <span class="mdc-chip">
                <span class="mdc-chip__ripple"></span>
                <span class="mdc-chip__text">Classes</span>
            </span>
            <h2>Créez des classes</h2>
        </div>
        <div class="card-content">
            <p class="card-title">Aucune classe créée pour l'instant</p>
            <p class="card-text">Vous devez créer des classes (6EME 1, 6EME 2, CE1 A etc.) avant d'inscrire les élèves.</p>
        </div>
        <div class="card-actions">
            <a href="{% url 'school:levels' %}" 
               hx-get="{% url 'school:levels' %}" 
               hx-push-url="{% url 'school:levels' %}" 
               hx-target="#app-content" 
               class="mdc-button mdc-button--raised">
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">Créer</span>
            </a>
        </div>
    </div>
    {% endif %}
    
    {% if not user.school.logo %}
    <div class="mdc-card action-card mdc-card--outlined" id="logo-card">
        <div class="mdc-card__ripple"></div>
        <div class="card-header">
            <span class="mdc-chip">
                <span class="mdc-chip__ripple"></span>
                <span class="mdc-chip__text">Logo</span>
            </span>
            <h2>Changez le logo de l'école</h2>
        </div>
        <div class="card-content">
            <form id="logo-form" method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="logo-preview">
                    <img src="{% static 'img/app_icon.png' %}" alt="Logo" height="50px" id="logoPreview" class="preview-image">
                </div>
                <div class="file-input-container">
                    <button type="button" class="mdc-button file-select-button">
                        <span class="mdc-button__ripple"></span>
                        <span class="material-icons mdc-button__icon">upload_file</span>
                        <span class="mdc-button__label">Sélectionner un fichier</span>
                    </button>
                    <input type="file" name="logo" id="logo" class="file-input" accept="image/*">
                </div>
            </form>
        </div>
        <div class="card-actions">
            <button type="submit" form="logo-form" class="mdc-button mdc-button--raised">
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">Valider</span>
            </button>
            <button type="button" class="mdc-button" onclick="dismissCard('logo-card')">
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">Non, ça va</span>
            </button>
        </div>
    </div>
    {% endif %}
    
    {% if not user.photo %}
    <div class="mdc-card action-card mdc-card--outlined" id="photo-card">
        <div class="mdc-card__ripple"></div>
        <div class="card-header">
            <span class="mdc-chip">
                <span class="mdc-chip__ripple"></span>
                <span class="mdc-chip__text">Photo</span>
            </span>
            <h2>Changez votre photo de profil</h2>
        </div>
        <div class="card-content">
            <form id="photo-form" method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="photo-preview">
                    <img src="{% static 'img/profile_pic.png' %}" alt="Photo" height="50px" id="photoPreview" class="preview-image preview-image-circle">
                </div>
                <div class="file-input-container">
                    <button type="button" class="mdc-button file-select-button">
                        <span class="mdc-button__ripple"></span>
                        <span class="material-icons mdc-button__icon">upload_file</span>
                        <span class="mdc-button__label">Sélectionner un fichier</span>
                    </button>
                    <input type="file" name="photo" id="photo" class="file-input" accept="image/*">
                </div>
            </form>
        </div>
        <div class="card-actions">
            <button type="submit" form="photo-form" class="mdc-button mdc-button--raised">
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">Valider</span>
            </button>
            <button type="button" class="mdc-button" onclick="dismissCard('photo-card')">
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">Non, ça va</span>
            </button>
        </div>
    </div>
    {% endif %}
</div>

<!-- Student and Teacher Stats -->
<div class="stats-section">
    <h2 class="section-title">Effectifs Elèves et Enseignants</h2>
    <div class="stats-cards">
        <!-- Boys Card -->
        <div class="mdc-card stats-card mdc-card--outlined">
            <div class="mdc-card__ripple"></div>
            <div class="card-content">
                <div class="card-icon blue-bg">
                    <span class="material-icons">boy</span>
                </div>
                <div class="card-data">
                    <h2>{{ data.boys|intcomma|default:"0" }}</h2>
                    <p>Garçons</p>
                    
                    {% if has_both_cycles %}
                    <div class="card-subcategories">
                        <div class="subcategory">
                            <span class="subcategory-value">{{ data.boys_primary|intcomma|default:"0" }}</span>
                            <span class="subcategory-label">Primaire</span>
                        </div>
                        <div class="subcategory">
                            <span class="subcategory-value">{{ data.boys_secondary|intcomma|default:"0" }}</span>
                            <span class="subcategory-label">Secondaire</span>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Girls Card -->
        <div class="mdc-card stats-card mdc-card--outlined">
            <div class="mdc-card__ripple"></div>
            <div class="card-content">
                <div class="card-icon pink-bg">
                    <span class="material-icons">girl</span>
                </div>
                <div class="card-data">
                    <h2>{{ data.girls|intcomma|default:"0" }}</h2>
                    <p>Filles</p>
                    
                    {% if has_both_cycles %}
                    <div class="card-subcategories">
                        <div class="subcategory">
                            <span class="subcategory-value">{{ data.girls_primary|intcomma|default:"0" }}</span>
                            <span class="subcategory-label">Primaire</span>
                        </div>
                        <div class="subcategory">
                            <span class="subcategory-value">{{ data.girls_secondary|intcomma|default:"0" }}</span>
                            <span class="subcategory-label">Secondaire</span>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Total Students Card -->
        <div class="mdc-card stats-card mdc-card--outlined">
            <div class="mdc-card__ripple"></div>
            <div class="card-content">
                <div class="card-icon green-bg">
                    <span class="material-icons">groups</span>
                </div>
                <div class="card-data">
                    <h2>{{ data.students|intcomma|default:"0" }}</h2>
                    <p>Total Élèves</p>
                    
                    {% if has_both_cycles %}
                    <div class="card-subcategories">
                        <div class="subcategory">
                            <span class="subcategory-value">{{ data.students_primary|intcomma|default:"0" }}</span>
                            <span class="subcategory-label">Primaire</span>
                        </div>
                        <div class="subcategory">
                            <span class="subcategory-value">{{ data.students_secondary|intcomma|default:"0" }}</span>
                            <span class="subcategory-label">Secondaire</span>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Teachers Card -->
        <div class="mdc-card stats-card mdc-card--outlined">
            <div class="mdc-card__ripple"></div>
            <div class="card-content">
                <div class="card-icon purple-bg">
                    <span class="material-icons">school</span>
                </div>
                <div class="card-data">
                    <h2>{{ teachers_count|intcomma|default:"0" }}</h2>
                    <p>Enseignants</p>
                </div>
            </div>
        </div>
    </div>
</div>

{% if user.role == ROLE_ACCOUNTANT or user.role == ROLE_FOUNDER %}
<!-- Daily Payments Stats -->
<div class="stats-section">
    <h2 class="section-title">Versements du Jour</h2>
    <div class="stats-cards">
        <!-- Registration Fees Card -->
        <div class="mdc-card stats-card mdc-card--outlined">
            <div class="mdc-card__ripple"></div>
            <div class="card-content">
                <div class="card-icon amber-bg">
                    <span class="material-icons">payments</span>
                </div>
                <div class="card-data">
                    <h2>{{ data.inscription_today|intcomma|default:"0" }} F</h2>
                    <p>Frais d'inscription</p>
                </div>
            </div>
        </div>
        
        <!-- Tuition Fees Card -->
        <div class="mdc-card stats-card mdc-card--outlined">
            <div class="mdc-card__ripple"></div>
            <div class="card-content">
                <div class="card-icon amber-bg">
                    <span class="material-icons">payments</span>
                </div>
                <div class="card-data">
                    <h2>{{ data.scolarite_today|intcomma|default:"0" }} F</h2>
                    <p>Scolarité</p>
                </div>
            </div>
        </div>
        
        <!-- Annex Fees Card -->
        <div class="mdc-card stats-card mdc-card--outlined">
            <div class="mdc-card__ripple"></div>
            <div class="card-content">
                <div class="card-icon amber-bg">
                    <span class="material-icons">payments</span>
                </div>
                <div class="card-data">
                    <h2>{{ data.annexe_today|intcomma|default:"0" }} F</h2>
                    <p>Annexe</p>
                </div>
            </div>
        </div>
        
        <!-- Total Daily Payments Card -->
        <div class="mdc-card stats-card mdc-card--outlined">
            <div class="mdc-card__ripple"></div>
            <div class="card-content">
                <div class="card-icon amber-bg">
                    <span class="material-icons">payments</span>
                </div>
                <div class="card-data">
                    <h2>{{ data.total_paid_today|intcomma|default:"0" }} F</h2>
                    <p>Total</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Yearly Payments Stats -->
<div class="stats-section">
    <h2 class="section-title">Cumul des Versements {{ year }}</h2>
    <div class="stats-cards">
        <!-- Registration Fees Card -->
        <div class="mdc-card stats-card mdc-card--outlined">
            <div class="mdc-card__ripple"></div>
            <div class="card-content">
                <div class="card-icon amber-bg">
                    <span class="material-icons">payments</span>
                </div>
                <div class="card-data">
                    <h2>{{ data.inscription|intcomma|default:"0" }} F</h2>
                    <p>Frais d'inscription</p>
                </div>
            </div>
        </div>
        
        <!-- Tuition Fees Card -->
        <div class="mdc-card stats-card mdc-card--outlined">
            <div class="mdc-card__ripple"></div>
            <div class="card-content">
                <div class="card-icon amber-bg">
                    <span class="material-icons">payments</span>
                </div>
                <div class="card-data">
                    <h2>{{ data.scolarite|intcomma|default:"0" }} F</h2>
                    <p>Scolarité</p>
                </div>
            </div>
        </div>
        
        <!-- Annex Fees Card -->
        <div class="mdc-card stats-card mdc-card--outlined">
            <div class="mdc-card__ripple"></div>
            <div class="card-content">
                <div class="card-icon amber-bg">
                    <span class="material-icons">payments</span>
                </div>
                <div class="card-data">
                    <h2>{{ data.annexe|intcomma|default:"0" }} F</h2>
                    <p>Annexe</p>
                </div>
            </div>
        </div>
        
        <!-- Total Yearly Payments Card -->
        <div class="mdc-card stats-card mdc-card--outlined">
            <div class="mdc-card__ripple"></div>
            <div class="card-content">
                <div class="card-icon amber-bg">
                    <span class="material-icons">payments</span>
                </div>
                <div class="card-data">
                    <h2>{{ data.total_paid|intcomma|default:"0" }} F</h2>
                    <p>Total</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% else %}
    {% include "partials/teacher/teacher_menu.html" with subtitle=subtitle %}
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    /* Dashboard specific styles
    .dashboard-header {
        margin-bottom: var(--md-spacing-3);
    }
    
    .dashboard-header h1 {
        margin-bottom: var(--md-spacing-2);
    } */
    
    .action-cards {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: var(--md-spacing-3);
        margin-bottom: var(--md-spacing-3);
    }
    
    .action-card {
        padding: 0;
        overflow: hidden;
    }
    
    .action-card .card-header {
        padding: var(--md-spacing-2) var(--md-spacing-2) 0;
    }
    
    .action-card .card-header h2 {
        font-size: 1rem;
        margin: var(--md-spacing-1) 0;
        color: var(--md-on-surface);
    }
    
    .action-card .card-content {
        padding: var(--md-spacing-2);
    }
    
    .action-card .card-title {
        font-size: 1rem;
        font-weight: 500;
        margin-bottom: var(--md-spacing-1);
        color: var(--md-on-surface);
    }
    
    .action-card .card-text {
        color: var(--md-on-surface-variant);
        font-size: 0.875rem;
        margin-bottom: var(--md-spacing-1);
    }
    
    .action-card .card-actions {
        padding: var(--md-spacing-1) var(--md-spacing-2);
        display: flex;
        justify-content: flex-end;
        gap: var(--md-spacing-1);
        border-top: 1px solid var(--md-outline-variant);
    }
    
    /* .search-container {
        display: flex;
        gap: var(--md-spacing-2);
        margin-bottom: var(--md-spacing-3);
    }
    
    .search-field {
        flex: 1;
    } */
    
    .section-title {
        font-size: 1.25rem;
        font-weight: 500;
        margin-bottom: var(--md-spacing-2);
        color: var(--md-on-surface-variant);
    }
    
    .stats-section {
        margin-bottom: var(--md-spacing-4);
    }
    
    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: var(--md-spacing-3);
    }
    
    .stats-card {
        padding: var(--md-spacing-2);
        border-radius: 16px;
        transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                    transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
    }
    
    .stats-card:hover {
        box-shadow: var(--md-elevation-level2);
        transform: translateY(-2px);
    }
    
    .card-content {
        display: flex;
        align-items: center;
        gap: var(--md-spacing-2);
    }
    
    .card-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }
    
    .blue-bg { background-color: #1976d2; }
    .pink-bg { background-color: #e91e63; }
    .green-bg { background-color: #4caf50; }
    .purple-bg { background-color: #9c27b0; }
    .amber-bg { background-color: #ff9800; }
    
    .card-data {
        flex: 1;
    }
    
    .card-data h2 {
        font-size: 1.5rem;
        font-weight: 500;
        margin: 0;
        color: var(--md-on-surface);
    }
    
    .card-data p {
        font-size: 0.875rem;
        color: var(--md-on-surface-variant);
        margin: 0;
    }
    
    .card-subcategories {
        display: flex;
        gap: var(--md-spacing-2);
        margin-top: var(--md-spacing-1);
    }
    
    .subcategory {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    
    .subcategory-value {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--md-on-surface);
    }
    
    .subcategory-label {
        font-size: 0.75rem;
        color: var(--md-on-surface-variant);
    }
    
    .file-input-container {
        margin-top: var(--md-spacing-2);
    }
    
    .file-input {
        display: none;
    }
    
    .preview-image {
        max-width: 100px;
        max-height: 100px;
        object-fit: contain;
        border: 1px solid var(--md-outline-variant);
        padding: 4px;
    }
    
    .preview-image-circle {
        border-radius: 50%;
    }
    
    /* Banner styles */
    .mdc-banner {
        background-color: #fff3cd;
        margin-bottom: var(--md-spacing-3);
        border-radius: 8px;
        overflow: hidden;
    }
    
    .mdc-banner__content {
        padding: var(--md-spacing-2);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .mdc-banner__graphic-text-wrapper {
        display: flex;
        align-items: center;
        gap: var(--md-spacing-2);
    }
    
    .mdc-banner__graphic {
        color: #856404;
    }
    
    .mdc-banner__text {
        color: #856404;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .stats-cards {
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        }
        
        .action-cards {
            grid-template-columns: 1fr;
        }
        
        .search-container {
            flex-direction: column;
        }
        
        .search-button {
            align-self: flex-end;
        }
    }
    
    @media (max-width: 480px) {
        .stats-cards {
            grid-template-columns: 1fr;
        }
        
        .card-content {
            flex-direction: column;
            text-align: center;
        }
        
        .card-data {
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Material Components
        const textFields = document.querySelectorAll('.mdc-text-field');
        textFields.forEach(textField => {
            mdc.textField.MDCTextField.attachTo(textField);
        });
        
        // Initialize banner if present
        const bannerElement = document.querySelector('.mdc-banner');
        if (bannerElement) {
            const banner = new mdc.banner.MDCBanner(bannerElement);
            banner.open();
        }
        
        // File input preview for logo
        const logoInput = document.getElementById('logo');
        const logoPreview = document.getElementById('logoPreview');
        
        if (logoInput && logoPreview) {
            const logoFileButton = logoInput.previousElementSibling;
            logoFileButton.addEventListener('click', () => {
                logoInput.click();
            });
            
            logoInput.addEventListener('change', function(event) {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        logoPreview.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }
            });
        }
        
        // File input preview for photo
        const photoInput = document.getElementById('photo');
        const photoPreview = document.getElementById('photoPreview');
        
        if (photoInput && photoPreview) {
            const photoFileButton = photoInput.previousElementSibling;
            photoFileButton.addEventListener('click', () => {
                photoInput.click();
            });
            
            photoInput.addEventListener('change', function(event) {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        photoPreview.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }
            });
        }
    });
    
    // Function to dismiss cards and save preference
    function dismissCard(cardId) {
        const card = document.getElementById(cardId);
        if (card) {
            card.style.display = 'none';
            localStorage.setItem(cardId + 'Preference', 'false');
        }
    }
    
    // Check for saved preferences
    const logoCardPreference = localStorage.getItem('logo-cardPreference');
    if (logoCardPreference === 'false') {
        const logoCard = document.getElementById('logo-card');
        if (logoCard) {
            logoCard.style.display = 'none';
        }
    }
    
    const photoCardPreference = localStorage.getItem('photo-cardPreference');
    if (photoCardPreference === 'false') {
        const photoCard = document.getElementById('photo-card');
        if (photoCard) {
            photoCard.style.display = 'none';
        }
    }
</script>
{% endblock %}
