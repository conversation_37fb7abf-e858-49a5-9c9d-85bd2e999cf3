<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS System - Dashboard</title>

    <!-- Material Icons and Roboto Font -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Material Components Web CSS -->
    <link href="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <!-- Material Design Preloader -->
    <div class="preloader">
        <div class="spinner">
            <svg class="circular" viewBox="25 25 50 50">
                <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="3" stroke-miterlimit="10"/>
            </svg>
        </div>
    </div>

    <div class="app-container">
        <!-- Top App Bar -->
        <header class="mdc-top-app-bar app-bar">
            <div class="mdc-top-app-bar__row">
                <section class="mdc-top-app-bar__section mdc-top-app-bar__section--align-start">
                    <button class="material-icons mdc-top-app-bar__navigation-icon mdc-icon-button" id="menu-button" aria-label="Menu">
                        <div class="mdc-icon-button__ripple"></div>
                        menu
                    </button>
                    <span class="mdc-top-app-bar__title">POS System</span>
                </section>
                <section class="mdc-top-app-bar__section mdc-top-app-bar__section--align-end">
                    <!-- Desktop Search Bar (visible on larger screens) -->
                    <div class="search-container desktop-search">
                        <div class="mdc-text-field mdc-text-field--filled mdc-text-field--with-leading-icon mdc-text-field--no-label search-field">
                            <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">search</span>
                            <input class="mdc-text-field__input" type="text" placeholder="Search...">
                            <span class="mdc-line-ripple"></span>
                        </div>
                    </div>

                    <!-- Mobile Search Icon (visible on smaller screens) -->
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button mobile-search-icon" id="mobile-search-button" aria-label="Search">
                        <div class="mdc-icon-button__ripple"></div>
                        search
                    </button>

                    <!-- Mobile Search Bar (hidden by default, shown when search icon is clicked) -->
                    <div class="mobile-search-container" id="mobile-search-container">
                        <div class="mobile-search-inner">
                            <span class="material-icons mobile-search-icon">search</span>
                            <input type="text" class="mobile-search-input" placeholder="Search...">
                            <button class="material-icons mdc-icon-button mobile-search-close" id="mobile-search-close">
                                <div class="mdc-icon-button__ripple"></div>
                                close
                            </button>
                        </div>
                    </div>
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" id="theme-toggle-button" aria-label="Toggle dark mode">
                        <div class="mdc-icon-button__ripple"></div>
                        light_mode
                    </button>
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" aria-label="Notifications" style="display: none;">
                        <div class="mdc-icon-button__ripple"></div>
                        notifications
                    </button>
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" aria-label="User">
                        <div class="mdc-icon-button__ripple"></div>
                        account_circle
                    </button>
                </section>
            </div>
        </header>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Sidebar Backdrop -->
            <div class="sidebar-backdrop" id="sidebar-backdrop"></div>
            <!-- Sidebar -->
            <aside class="sidebar closed" id="sidebar">
                <div class="mdc-drawer__header">
                    <h3 class="mdc-drawer__title">POS Menu</h3>
                    <h6 class="mdc-drawer__subtitle">Navigation</h6>
                </div>
                <div class="mdc-drawer__content">
                    <nav class="mdc-list">
                        <a class="mdc-list-item mdc-list-item--activated" href="#" aria-current="page">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">dashboard</span>
                            <span class="mdc-list-item__text">Dashboard</span>
                        </a>
                        <a class="mdc-list-item" href="#">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">shopping_cart</span>
                            <span class="mdc-list-item__text">New Sale</span>
                        </a>
                        <a class="mdc-list-item" href="#">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">inventory</span>
                            <span class="mdc-list-item__text">Products</span>
                        </a>
                        <a class="mdc-list-item" href="#">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">people</span>
                            <span class="mdc-list-item__text">Customers</span>
                        </a>
                        <a class="mdc-list-item" href="#">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">receipt_long</span>
                            <span class="mdc-list-item__text">Transactions</span>
                        </a>
                        <!-- Analytics with nested subitems -->
                        <div class="mdc-list-item mdc-list-item--collapsible">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">analytics</span>
                            <span class="mdc-list-item__text">Analytics</span>
                            <button class="mdc-icon-button mdc-list-item__meta" aria-label="Expand Analytics menu">
                                <div class="mdc-icon-button__ripple"></div>
                                <span class="material-icons">expand_more</span>
                            </button>
                        </div>
                        <div class="mdc-list-group mdc-list-group--hidden">
                            <a class="mdc-list-item mdc-list-item--nested" href="#">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic">bar_chart</span>
                                <span class="mdc-list-item__text">Sales Analytics</span>
                            </a>
                            <a class="mdc-list-item mdc-list-item--nested" href="#">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic">trending_up</span>
                                <span class="mdc-list-item__text">Growth Metrics</span>
                            </a>
                            <a class="mdc-list-item mdc-list-item--nested" href="#">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic">people_outline</span>
                                <span class="mdc-list-item__text">Customer Analytics</span>
                            </a>
                        </div>

                        <a class="mdc-list-item" href="#">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">bar_chart</span>
                            <span class="mdc-list-item__text">Reports</span>
                        </a>
                        <a class="mdc-list-item" href="#">
                            <span class="mdc-list-item__ripple"></span>
                            <span class="material-icons mdc-list-item__graphic">settings</span>
                            <span class="mdc-list-item__text">Settings</span>
                        </a>
                    </nav>
                </div>
            </aside>

            <!-- Dashboard Content -->
            <main class="dashboard-content">
                <div class="page-content">
                    <h1>Dashboard</h1>

                    <!-- Stats Cards -->
                    <div class="stats-cards">
                        <div class="mdc-card stats-card mdc-card--outlined">
                            <div class="mdc-card__ripple"></div>
                            <div class="card-content">
                                <div class="card-icon">
                                    <span class="material-icons">payments</span>
                                </div>
                                <div class="card-data">
                                    <h2>$1,250.00</h2>
                                    <p>Today's Sales</p>
                                </div>
                            </div>
                        </div>

                        <div class="mdc-card stats-card mdc-card--outlined">
                            <div class="mdc-card__ripple"></div>
                            <div class="card-content">
                                <div class="card-icon">
                                    <span class="material-icons">receipt</span>
                                </div>
                                <div class="card-data">
                                    <h2>24</h2>
                                    <p>Transactions</p>
                                </div>
                            </div>
                        </div>

                        <div class="mdc-card stats-card mdc-card--outlined">
                            <div class="mdc-card__ripple"></div>
                            <div class="card-content">
                                <div class="card-icon">
                                    <span class="material-icons">person</span>
                                </div>
                                <div class="card-data">
                                    <h2>8</h2>
                                    <p>New Customers</p>
                                </div>
                            </div>
                        </div>

                        <div class="mdc-card stats-card mdc-card--outlined">
                            <div class="mdc-card__ripple"></div>
                            <div class="card-content">
                                <div class="card-icon">
                                    <span class="material-icons">inventory_2</span>
                                </div>
                                <div class="card-data">
                                    <h2>5</h2>
                                    <p>Low Stock Items</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Transactions -->
                    <div class="mdc-card recent-transactions mdc-card--outlined">
                        <div class="mdc-card__ripple"></div>
                        <h2 class="card-title">Recent Transactions</h2>
                        <div class="mdc-data-table">
                            <div class="mdc-data-table__table-container">
                                <table class="mdc-data-table__table">
                                    <thead>
                                        <tr class="mdc-data-table__header-row">
                                            <th class="mdc-data-table__header-cell">Transaction ID</th>
                                            <th class="mdc-data-table__header-cell">Customer</th>
                                            <th class="mdc-data-table__header-cell">Items</th>
                                            <th class="mdc-data-table__header-cell">Amount</th>
                                            <th class="mdc-data-table__header-cell">Time</th>
                                            <th class="mdc-data-table__header-cell">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody class="mdc-data-table__content">
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell">#TRX-1234</td>
                                            <td class="mdc-data-table__cell">John Doe</td>
                                            <td class="mdc-data-table__cell">3</td>
                                            <td class="mdc-data-table__cell">$125.00</td>
                                            <td class="mdc-data-table__cell">10:30 AM</td>
                                            <td class="mdc-data-table__cell"><span class="status-completed">Completed</span></td>
                                        </tr>
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell">#TRX-1234</td>
                                            <td class="mdc-data-table__cell">John Doe</td>
                                            <td class="mdc-data-table__cell">3</td>
                                            <td class="mdc-data-table__cell">$125.00</td>
                                            <td class="mdc-data-table__cell">10:30 AM</td>
                                            <td class="mdc-data-table__cell"><span class="status-completed">Completed</span></td>
                                        </tr>
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell">#TRX-1234</td>
                                            <td class="mdc-data-table__cell">John Doe</td>
                                            <td class="mdc-data-table__cell">3</td>
                                            <td class="mdc-data-table__cell">$125.00</td>
                                            <td class="mdc-data-table__cell">10:30 AM</td>
                                            <td class="mdc-data-table__cell"><span class="status-completed">Completed</span></td>
                                        </tr>
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell">#TRX-1234</td>
                                            <td class="mdc-data-table__cell">John Doe</td>
                                            <td class="mdc-data-table__cell">3</td>
                                            <td class="mdc-data-table__cell">$125.00</td>
                                            <td class="mdc-data-table__cell">10:30 AM</td>
                                            <td class="mdc-data-table__cell"><span class="status-completed">Completed</span></td>
                                        </tr>
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell">#TRX-1234</td>
                                            <td class="mdc-data-table__cell">John Doe</td>
                                            <td class="mdc-data-table__cell">3</td>
                                            <td class="mdc-data-table__cell">$125.00</td>
                                            <td class="mdc-data-table__cell">10:30 AM</td>
                                            <td class="mdc-data-table__cell"><span class="status-completed">Completed</span></td>
                                        </tr>
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell">#TRX-1234</td>
                                            <td class="mdc-data-table__cell">John Doe</td>
                                            <td class="mdc-data-table__cell">3</td>
                                            <td class="mdc-data-table__cell">$125.00</td>
                                            <td class="mdc-data-table__cell">10:30 AM</td>
                                            <td class="mdc-data-table__cell"><span class="status-completed">Completed</span></td>
                                        </tr>
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell">#TRX-1234</td>
                                            <td class="mdc-data-table__cell">John Doe</td>
                                            <td class="mdc-data-table__cell">3</td>
                                            <td class="mdc-data-table__cell">$125.00</td>
                                            <td class="mdc-data-table__cell">10:30 AM</td>
                                            <td class="mdc-data-table__cell"><span class="status-completed">Completed</span></td>
                                        </tr>
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell">#TRX-1234</td>
                                            <td class="mdc-data-table__cell">John Doe</td>
                                            <td class="mdc-data-table__cell">3</td>
                                            <td class="mdc-data-table__cell">$125.00</td>
                                            <td class="mdc-data-table__cell">10:30 AM</td>
                                            <td class="mdc-data-table__cell"><span class="status-completed">Completed</span></td>
                                        </tr>
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell">#TRX-1234</td>
                                            <td class="mdc-data-table__cell">John Doe</td>
                                            <td class="mdc-data-table__cell">3</td>
                                            <td class="mdc-data-table__cell">$125.00</td>
                                            <td class="mdc-data-table__cell">10:30 AM</td>
                                            <td class="mdc-data-table__cell"><span class="status-completed">Completed</span></td>
                                        </tr>
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell">#TRX-1234</td>
                                            <td class="mdc-data-table__cell">John Doe</td>
                                            <td class="mdc-data-table__cell">3</td>
                                            <td class="mdc-data-table__cell">$125.00</td>
                                            <td class="mdc-data-table__cell">10:30 AM</td>
                                            <td class="mdc-data-table__cell"><span class="status-completed">Completed</span></td>
                                        </tr>
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell">#TRX-1234</td>
                                            <td class="mdc-data-table__cell">John Doe</td>
                                            <td class="mdc-data-table__cell">3</td>
                                            <td class="mdc-data-table__cell">$125.00</td>
                                            <td class="mdc-data-table__cell">10:30 AM</td>
                                            <td class="mdc-data-table__cell"><span class="status-completed">Completed</span></td>
                                        </tr>
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell">#TRX-1234</td>
                                            <td class="mdc-data-table__cell">John Doe</td>
                                            <td class="mdc-data-table__cell">3</td>
                                            <td class="mdc-data-table__cell">$125.00</td>
                                            <td class="mdc-data-table__cell">10:30 AM</td>
                                            <td class="mdc-data-table__cell"><span class="status-completed">Completed</span></td>
                                        </tr>
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell">#TRX-1234</td>
                                            <td class="mdc-data-table__cell">John Doe</td>
                                            <td class="mdc-data-table__cell">3</td>
                                            <td class="mdc-data-table__cell">$125.00</td>
                                            <td class="mdc-data-table__cell">10:30 AM</td>
                                            <td class="mdc-data-table__cell"><span class="status-completed">Completed</span></td>
                                        </tr>
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell">#TRX-1234</td>
                                            <td class="mdc-data-table__cell">John Doe</td>
                                            <td class="mdc-data-table__cell">3</td>
                                            <td class="mdc-data-table__cell">$125.00</td>
                                            <td class="mdc-data-table__cell">10:30 AM</td>
                                            <td class="mdc-data-table__cell"><span class="status-completed">Completed</span></td>
                                        </tr>
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell">#TRX-1234</td>
                                            <td class="mdc-data-table__cell">John Doe</td>
                                            <td class="mdc-data-table__cell">3</td>
                                            <td class="mdc-data-table__cell">$125.00</td>
                                            <td class="mdc-data-table__cell">10:30 AM</td>
                                            <td class="mdc-data-table__cell"><span class="status-completed">Completed</span></td>
                                        </tr>
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell">#TRX-1234</td>
                                            <td class="mdc-data-table__cell">John Doe</td>
                                            <td class="mdc-data-table__cell">3</td>
                                            <td class="mdc-data-table__cell">$125.00</td>
                                            <td class="mdc-data-table__cell">10:30 AM</td>
                                            <td class="mdc-data-table__cell"><span class="status-completed">Completed</span></td>
                                        </tr>
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell">#TRX-1234</td>
                                            <td class="mdc-data-table__cell">John Doe</td>
                                            <td class="mdc-data-table__cell">3</td>
                                            <td class="mdc-data-table__cell">$125.00</td>
                                            <td class="mdc-data-table__cell">10:30 AM</td>
                                            <td class="mdc-data-table__cell"><span class="status-completed">Completed</span></td>
                                        </tr>
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell">#TRX-1233</td>
                                            <td class="mdc-data-table__cell">Jane Smith</td>
                                            <td class="mdc-data-table__cell">1</td>
                                            <td class="mdc-data-table__cell">$45.50</td>
                                            <td class="mdc-data-table__cell">10:15 AM</td>
                                            <td class="mdc-data-table__cell"><span class="status-completed">Completed</span></td>
                                        </tr>
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell">#TRX-1232</td>
                                            <td class="mdc-data-table__cell">Robert Johnson</td>
                                            <td class="mdc-data-table__cell">5</td>
                                            <td class="mdc-data-table__cell">$210.75</td>
                                            <td class="mdc-data-table__cell">9:45 AM</td>
                                            <td class="mdc-data-table__cell"><span class="status-pending">Pending</span></td>
                                        </tr>
                                        <tr class="mdc-data-table__row">
                                            <td class="mdc-data-table__cell">#TRX-1231</td>
                                            <td class="mdc-data-table__cell">Emily Davis</td>
                                            <td class="mdc-data-table__cell">2</td>
                                            <td class="mdc-data-table__cell">$78.25</td>
                                            <td class="mdc-data-table__cell">9:30 AM</td>
                                            <td class="mdc-data-table__cell"><span class="status-completed">Completed</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- User Profile Offcanvas (Custom Implementation) -->
    <div class="user-drawer-container" id="user-drawer-container">
        <div class="user-drawer-backdrop" id="user-drawer-backdrop"></div>
        <div class="user-drawer" id="user-drawer">
            <div class="user-drawer-header">
                <button class="material-icons mdc-icon-button close-drawer-button" id="close-user-drawer">
                    <div class="mdc-icon-button__ripple"></div>
                    close
                </button>
                <div class="user-profile-header">
                    <div class="user-avatar">
                        <span class="material-icons">account_circle</span>
                    </div>
                    <h3 class="user-drawer-title">John Doe</h3>
                    <h6 class="user-drawer-subtitle"><EMAIL></h6>
                </div>
            </div>
            <div class="user-drawer-content">
                <nav class="mdc-list">
                    <a class="mdc-list-item" href="#" style="--item-index: 0;">
                        <span class="mdc-list-item__ripple"></span>
                        <span class="material-icons mdc-list-item__graphic">person</span>
                        <span class="mdc-list-item__text">My Profile</span>
                    </a>
                    <a class="mdc-list-item" href="#" style="--item-index: 1;">
                        <span class="mdc-list-item__ripple"></span>
                        <span class="material-icons mdc-list-item__graphic">settings</span>
                        <span class="mdc-list-item__text">Settings</span>
                    </a>
                    <a class="mdc-list-item" href="#" style="--item-index: 2;">
                        <span class="mdc-list-item__ripple"></span>
                        <span class="material-icons mdc-list-item__graphic">help</span>
                        <span class="mdc-list-item__text">Help & Support</span>
                    </a>
                    <hr class="mdc-list-divider">
                    <a class="mdc-list-item" href="#" style="--item-index: 3;">
                        <span class="mdc-list-item__ripple"></span>
                        <span class="material-icons mdc-list-item__graphic">logout</span>
                        <span class="mdc-list-item__text">Sign Out</span>
                    </a>
                </nav>
            </div>
        </div>
    </div>

    <!-- Bottom App Bar for Mobile -->
    <div class="bottom-app-bar" id="bottom-app-bar">
        <div class="bottom-app-bar-inner">
            <button class="bottom-nav-item active">
                <div class="bottom-nav-ripple-container">
                    <div class="bottom-nav-ripple"></div>
                </div>
                <span class="material-icons">dashboard</span>
                <span class="bottom-nav-label">Dashboard</span>
            </button>
            <button class="bottom-nav-item">
                <div class="bottom-nav-ripple-container">
                    <div class="bottom-nav-ripple"></div>
                </div>
                <span class="material-icons">shopping_cart</span>
                <span class="bottom-nav-label">Orders</span>
            </button>
            <button class="bottom-nav-item">
                <div class="bottom-nav-ripple-container">
                    <div class="bottom-nav-ripple"></div>
                </div>
                <span class="material-icons">inventory</span>
                <span class="bottom-nav-label">Products</span>
            </button>
            <button class="bottom-nav-item">
                <div class="bottom-nav-ripple-container">
                    <div class="bottom-nav-ripple"></div>
                </div>
                <span class="material-icons">people</span>
                <span class="bottom-nav-label">Customers</span>
            </button>
            <button class="bottom-nav-item">
                <div class="bottom-nav-ripple-container">
                    <div class="bottom-nav-ripple"></div>
                </div>
                <span class="material-icons">more_horiz</span>
                <span class="bottom-nav-label">More</span>
            </button>
        </div>
    </div>

    <!-- Notifications Menu -->
    <div class="mdc-menu-surface--anchor notifications-anchor">
        <div class="mdc-menu mdc-menu-surface notifications-menu" id="notifications-menu">
            <div class="notifications-header">
                <h3>Notifications</h3>
                <button class="mdc-button" id="mark-all-read">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Mark all as read</span>
                </button>
            </div>
            <ul class="mdc-list notifications-list">
                <li class="mdc-list-item notification-item unread">
                    <span class="mdc-list-item__ripple"></span>
                    <span class="material-icons notification-icon">shopping_cart</span>
                    <span class="mdc-list-item__text">
                        <span class="notification-title">New Order #1234</span>
                        <span class="notification-text">A new order has been placed</span>
                        <span class="notification-time">2 minutes ago</span>
                    </span>
                </li>
                <li class="mdc-list-item notification-item unread">
                    <span class="mdc-list-item__ripple"></span>
                    <span class="material-icons notification-icon">inventory</span>
                    <span class="mdc-list-item__text">
                        <span class="notification-title">Low Stock Alert</span>
                        <span class="notification-text">Product XYZ is running low</span>
                        <span class="notification-time">1 hour ago</span>
                    </span>
                </li>
                <li class="mdc-list-item notification-item">
                    <span class="mdc-list-item__ripple"></span>
                    <span class="material-icons notification-icon">person_add</span>
                    <span class="mdc-list-item__text">
                        <span class="notification-title">New Customer</span>
                        <span class="notification-text">Jane Smith registered as a new customer</span>
                        <span class="notification-time">Yesterday</span>
                    </span>
                </li>
                <li class="mdc-list-item notification-item">
                    <span class="mdc-list-item__ripple"></span>
                    <span class="material-icons notification-icon">system_update</span>
                    <span class="mdc-list-item__text">
                        <span class="notification-title">System Update</span>
                        <span class="notification-text">System will be updated tonight</span>
                        <span class="notification-time">2 days ago</span>
                    </span>
                </li>
            </ul>
            <div class="notifications-footer">
                <a href="#" class="mdc-button">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">View All Notifications</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Material Components Web JavaScript -->
    <script src="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="js/script.js"></script>
</body>
</html>
