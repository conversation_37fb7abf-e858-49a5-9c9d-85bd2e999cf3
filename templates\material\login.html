<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS System - Login</title>

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Material Components Web CSS -->
    <link href="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="css/styles.css" rel="stylesheet">
    <link href="css/login.css" rel="stylesheet">
</head>
<body class="login-body">
    <!-- Preloader -->
    <div class="preloader">
        <div class="spinner">
            <svg class="circular" viewBox="25 25 50 50">
                <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="3" stroke-miterlimit="10"/>
            </svg>
        </div>
    </div>

    <div class="login-container">
        <div class="login-card mdc-card">
            <div class="login-header">
                <div class="login-logo">
                    <span class="material-icons">point_of_sale</span>
                </div>
                <h1 class="login-title">POS System</h1>
                <h2 class="login-subtitle">Sign in to continue</h2>
            </div>

            <div class="login-form">
                <!-- Email Field -->
                <div class="mdc-text-field mdc-text-field--filled login-field" id="email-field">
                    <span class="mdc-text-field__ripple"></span>
                    <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">email</span>
                    <input class="mdc-text-field__input" type="email" id="email" aria-labelledby="email-label" required>
                    <label class="mdc-floating-label" id="email-label">Email</label>
                    <span class="mdc-line-ripple"></span>
                </div>

                <!-- Password Field -->
                <div class="mdc-text-field mdc-text-field--filled login-field" id="password-field">
                    <span class="mdc-text-field__ripple"></span>
                    <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">lock</span>
                    <input class="mdc-text-field__input" type="password" id="password" aria-labelledby="password-label" required>
                    <label class="mdc-floating-label" id="password-label">Password</label>
                    <span class="mdc-line-ripple"></span>
                    <button class="material-icons mdc-text-field__icon mdc-text-field__icon--trailing password-toggle ripple" tabindex="0" role="button" style="border: none; height: auto">visibility_off</button>
                </div>

                <!-- Remember Me Checkbox -->
                <div class="login-options">
                    <div class="mdc-form-field">
                        <div class="mdc-checkbox">
                            <input type="checkbox" class="mdc-checkbox__native-control" id="remember-me"/>
                            <div class="mdc-checkbox__background">
                                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                    <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                </svg>
                                <div class="mdc-checkbox__mixedmark"></div>
                            </div>
                            <div class="mdc-checkbox__ripple"></div>
                        </div>
                        <label for="remember-me">Remember me</label>
                    </div>

                    <a href="index.html" class="forgot-password">Forgot password?</a>
                </div>

                <!-- Login Button -->
                <button class="mdc-button mdc-button--raised login-button" id="login-button">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Sign In</span>
                </button>

                <!-- Create Account Link -->
                <div class="create-account">
                    <span>Don't have an account?</span>
                    <a href="index.html" class="create-account-link">Create account</a>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="login-footer">
            <div class="footer-links">
                <a href="#">Privacy Policy</a>
                <span class="footer-divider">•</span>
                <a href="#">Terms of Service</a>
                <span class="footer-divider">•</span>
                <a href="#">Help</a>
            </div>
            <div class="footer-copyright">
                © 2023 POS System. All rights reserved.
            </div>
        </div>
    </div>

    <!-- Material Components Web JavaScript -->
    <script src="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="js/login.js"></script>
</body>
</html>
