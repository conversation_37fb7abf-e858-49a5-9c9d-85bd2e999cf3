{% load widget_tweaks %}
{% load humanize %}

<div method="post" action="" class="dashboard-wrapper" hx-include="this" hx-get="{{ request.path }}?page={{ page }}{% if education %}&education={{ education}}{% endif %}" hx-trigger="saved from:body" hx-target="#app-content">
  <!-- Filters Section -->
  {% include 'partials/active_students/filter_section.html' %}

  <!-- Table Section -->
  <div id="results-area">
    <!-- Enhanced Material Design Data Table -->
    <div class="mdc-card data-table-card mdc-card--outlined">
        <!-- Data Table Header with Actions -->
        <div class="data-table-header">
            <!-- Data Table Actions -->
            <div class="data-table-actions">
                <!-- Search Field -->
                <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--with-leading-icon search-field">
                    <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">search</span>
                    <input id="data-table-search" class="mdc-text-field__input" type="text" placeholder="Rechercher des étudiants...">
                    <div class="mdc-notched-outline">
                        <div class="mdc-notched-outline__leading"></div>
                        <div class="mdc-notched-outline__notch">
                            <label class="mdc-floating-label">Rechercher</label>
                        </div>
                        <div class="mdc-notched-outline__trailing"></div>
                    </div>
                </div>

                <!-- More Options Button -->
                <button class="mdc-icon-button" aria-label="Plus d'options">
                    <div class="mdc-icon-button__ripple"></div>
                    <span class="material-icons">more_vert</span>
                </button>
            </div>
        </div>

        <!-- Data Table -->
        <div class="mdc-data-table">
            <div class="mdc-data-table__table-container" style="max-height: 500px; overflow: auto;">
                <table class="mdc-data-table__table" style="font-size: 11.5px;">
                    <thead>
                        <tr class="mdc-data-table__header-row">
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--checkbox" width="40">
                                <div class="mdc-checkbox mdc-data-table__header-row-checkbox">
                                    <input type="checkbox" id="select-all" class="mdc-checkbox__native-control" aria-label="Sélectionner tout"/>
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                </div>
                            </th>
                            <th class="mdc-data-table__header-cell" width="60">Photo</th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort sticky-header sticky-column {% if sort_field == 'student__last_name' %}sort-active{% endif %}"
                                role="columnheader" scope="col" aria-sort="none"
                                hx-get="{{ request.path }}"
                                hx-include="[name='per_page']"
                                hx-vals='{"sort": "{% if sort_field == "student__last_name" and sort_direction == "asc" %}-{% endif %}student__last_name"}'>
                                <div class="mdc-data-table__header-cell-wrapper">
                                    <div class="mdc-data-table__header-cell-label">Nom et Prénoms</div>
                                    <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" aria-label="Trier par nom">
                                        {% if sort_field == 'student__last_name' %}
                                            {% if sort_direction == 'asc' %}keyboard_arrow_up{% else %}keyboard_arrow_down{% endif %}
                                        {% else %}
                                            keyboard_arrow_down
                                        {% endif %}
                                    </button>
                                </div>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort {% if sort_field == 'student__student_id' %}sort-active{% endif %}"
                                role="columnheader" scope="col" aria-sort="none"
                                hx-get="{{ request.path }}"
                                hx-include="[name='per_page']"
                                hx-vals='{"sort": "{% if sort_field == "student__student_id" and sort_direction == "asc" %}-{% endif %}student__student_id"}'>
                                <div class="mdc-data-table__header-cell-wrapper">
                                    <div class="mdc-data-table__header-cell-label">Matricule</div>
                                    <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" aria-label="Trier par matricule">
                                        {% if sort_field == 'student__student_id' %}
                                            {% if sort_direction == 'asc' %}keyboard_arrow_up{% else %}keyboard_arrow_down{% endif %}
                                        {% else %}
                                            keyboard_arrow_down
                                        {% endif %}
                                    </button>
                                </div>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort {% if sort_field == 'level_fr' %}sort-active{% endif %}"
                                role="columnheader" scope="col" aria-sort="none"
                                hx-get="{{ request.path }}"
                                hx-include="[name='per_page']"
                                hx-vals='{"sort": "{% if sort_field == "level_fr" and sort_direction == "asc" %}-{% endif %}level_fr"}'>
                                <div class="mdc-data-table__header-cell-wrapper">
                                    <div class="mdc-data-table__header-cell-label">Classe</div>
                                    <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" aria-label="Trier par classe">
                                        {% if sort_field == 'level_fr' %}
                                            {% if sort_direction == 'asc' %}keyboard_arrow_up{% else %}keyboard_arrow_down{% endif %}
                                        {% else %}
                                            keyboard_arrow_down
                                        {% endif %}
                                    </button>
                                </div>
                            </th>
                            {% if is_arabic_school %}
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort {% if sort_field == 'level_ar' %}sort-active{% endif %}"
                                role="columnheader" scope="col" aria-sort="none"
                                hx-get="{{ request.path }}"
                                hx-include="[name='per_page']"
                                hx-vals='{"sort": "{% if sort_field == "level_ar" and sort_direction == "asc" %}-{% endif %}level_ar"}'>
                                <div class="mdc-data-table__header-cell-wrapper">
                                    <div class="mdc-data-table__header-cell-label">Arabe</div>
                                    <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" aria-label="Trier par niveau arabe">
                                        {% if sort_field == 'level_ar' %}
                                            {% if sort_direction == 'asc' %}keyboard_arrow_up{% else %}keyboard_arrow_down{% endif %}
                                        {% else %}
                                            keyboard_arrow_down
                                        {% endif %}
                                    </button>
                                </div>
                            </th>
                            {% endif %}
                            <th class="mdc-data-table__header-cell">Sexe</th>
                            <th class="mdc-data-table__header-cell">Statut</th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort mdc-data-table__header-cell--numeric {% if sort_field == 'debt' %}sort-active{% endif %}"
                                role="columnheader" scope="col" aria-sort="none"
                                hx-get="{{ request.path }}"
                                hx-include="[name='per_page']"
                                hx-vals='{"sort": "{% if sort_field == "debt" and sort_direction == "asc" %}-{% endif %}debt"}'>
                                <div class="mdc-data-table__header-cell-wrapper">
                                    <div class="mdc-data-table__header-cell-label">Arriéré</div>
                                    <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" aria-label="Trier par arriéré">
                                        {% if sort_field == 'debt' %}
                                            {% if sort_direction == 'asc' %}keyboard_arrow_up{% else %}keyboard_arrow_down{% endif %}
                                        {% else %}
                                            keyboard_arrow_down
                                        {% endif %}
                                    </button>
                                </div>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort mdc-data-table__header-cell--numeric {% if sort_field == 'amount' %}sort-active{% endif %}"
                                role="columnheader" scope="col" aria-sort="none"
                                hx-get="{{ request.path }}"
                                hx-include="[name='per_page']"
                                hx-vals='{"sort": "{% if sort_field == "amount" and sort_direction == "asc" %}-{% endif %}amount"}'>
                                <div class="mdc-data-table__header-cell-wrapper">
                                    <div class="mdc-data-table__header-cell-label">À payer</div>
                                    <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" aria-label="Trier par montant à payer">
                                        {% if sort_field == 'amount' %}
                                            {% if sort_direction == 'asc' %}keyboard_arrow_up{% else %}keyboard_arrow_down{% endif %}
                                        {% else %}
                                            keyboard_arrow_down
                                        {% endif %}
                                    </button>
                                </div>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort mdc-data-table__header-cell--numeric {% if sort_field == 'paid' %}sort-active{% endif %}"
                                role="columnheader" scope="col" aria-sort="none"
                                hx-get="{{ request.path }}"
                                hx-include="[name='per_page']"
                                hx-vals='{"sort": "{% if sort_field == "paid" and sort_direction == "asc" %}-{% endif %}paid"}'>
                                <div class="mdc-data-table__header-cell-wrapper">
                                    <div class="mdc-data-table__header-cell-label">Payé</div>
                                    <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" aria-label="Trier par montant payé">
                                        {% if sort_field == 'paid' %}
                                            {% if sort_direction == 'asc' %}keyboard_arrow_up{% else %}keyboard_arrow_down{% endif %}
                                        {% else %}
                                            keyboard_arrow_down
                                        {% endif %}
                                    </button>
                                </div>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort mdc-data-table__header-cell--numeric {% if sort_field == 'remaining' %}sort-active{% endif %}"
                                role="columnheader" scope="col" aria-sort="none"
                                hx-get="{{ request.path }}"
                                hx-include="[name='per_page']"
                                hx-vals='{"sort": "{% if sort_field == "remaining" and sort_direction == "asc" %}-{% endif %}remaining"}'>
                                <div class="mdc-data-table__header-cell-wrapper">
                                    <div class="mdc-data-table__header-cell-label">Reste</div>
                                    <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" aria-label="Trier par reste à payer">
                                        {% if sort_field == 'remaining' %}
                                            {% if sort_direction == 'asc' %}keyboard_arrow_up{% else %}keyboard_arrow_down{% endif %}
                                        {% else %}
                                            keyboard_arrow_down
                                        {% endif %}
                                    </button>
                                </div>
                            </th>
                            <th class="mdc-data-table__header-cell" width="120">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="mdc-data-table__content">
                        {% for enrollment in enrollments %}
                        <tr class="mdc-data-table__row student-row {% if enrollment.selected %} selected {% endif %}">
                            <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                    <input type="checkbox"
                                           name="check-{{enrollment.id}}"
                                           id="check-{{enrollment.id}}"
                                           class="row-checkbox mdc-checkbox__native-control"
                                           {% if enrollment.selected %} checked="checked" {% endif %}/>
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="mdc-data-table__cell">
                                {% if enrollment.student.photo %}
                                  <img data-original="{{ enrollment.student.photo.url }}"
                                       alt="Photo"
                                       class="student-photo lazy"
                                       style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover;">
                                {% elif enrollment.is_second_cycle_fr and enrollment.student.student_id %}
                                  <img data-original="{{ enrollment.student.government_photo }}"
                                       alt="Photo"
                                       class="student-photo lazy"
                                       id="{{ enrollment.id }}"
                                       style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover;"
                                       onload="if (this.src.endsWith('CC')) {
                                         this.src = '{{ enrollment.student.blank_photo }}'
                                       }">
                                {% else %}
                                  <img data-original="{{ enrollment.student.blank_photo }}"
                                       alt="Photo"
                                       class="student-photo lazy"
                                       style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover;">
                                {% endif %}
                            </td>
                            <td class="mdc-data-table__cell sticky-column">
                                <div class="student-name">{{ enrollment.student.get_full_name }}
                                  {% if is_arabic_school and enrollment.student.full_name_ar %}<br> <span class="text-muted">{{ enrollment.student.full_name_ar }}</span>{% endif %}
                                </div>
                            </td>
                            <td class="mdc-data-table__cell">{{ enrollment.student.student_id|default:"-" }}</td>
                            <td class="mdc-data-table__cell">
                                {% if enrollment.level_fr or enrollment.generic_level_fr %}
                                    {{ enrollment.level_fr|default:enrollment.generic_level_fr }}
                                {% elif not enrollment.level_fr %}
                                    <span class="text-muted" title="Classe non attribuée" data-toggle="tooltip">?</span>
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            {% if is_arabic_school %}
                            <td class="mdc-data-table__cell">
                                {% if enrollment.level_ar or enrollment.generic_level_ar %}
                                    {{ enrollment.level_ar|default:enrollment.generic_level_ar }}
                                {% elif not enrollment.level_ar %}
                                    <span class="text-muted" title="Classe arabe non attribuée" data-toggle="tooltip">?</span>
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            {% endif %}
                            <td class="mdc-data-table__cell">{{ enrollment.student.gender }}</td>
                            <td class="mdc-data-table__cell">
                                {% if enrollment.status %}
                                <span class="mdc-chip {% if enrollment.active %}mdc-chip--success{% else %}mdc-chip--error{% endif %}">
                                  <span class="mdc-chip__text">{{ enrollment.status }}</span>
                                </span>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td class="mdc-data-table__cell mdc-data-table__cell--numeric amount-cell amount-negative">
                                {{ enrollment.debt|intcomma|default:"0" }}
                            </td>
                            <td class="mdc-data-table__cell mdc-data-table__cell--numeric amount-cell">
                                {{ enrollment.amount|intcomma }}
                            </td>
                            <td class="mdc-data-table__cell mdc-data-table__cell--numeric amount-cell {% if enrollment.paid %}amount-positive{% endif %}">
                                {% if enrollment.paid %} {{ enrollment.paid|intcomma|default:"0" }} {% else %} - {% endif %}
                            </td>
                            <td class="mdc-data-table__cell mdc-data-table__cell--numeric amount-cell {% if enrollment.remaining == 0 %}amount-positive{% elif enrollment.remaining > 0 %}amount-warning{% endif %}">
                                {% if enrollment.remaining == 0 and enrollment.get_fees_total > 0 %}
                                  Soldé
                                {% else %}
                                  {{ enrollment.remaining|intcomma|default:"-" }}
                                {% endif %}
                            </td>
                            <td class="mdc-data-table__cell">
                                <div class="mdc-data-table__row-actions d-flex justify-content-between" style="max-width: 180px !important;">
                                  {% if enrollment.active %}
                                  <!-- Edit Button -->
                                  <button class="mdc-icon-button show-on-phone"
                                          hx-get="{% url 'school:student_edit_wizard' enrollment.id %}?page={{ page }}"
                                          hx-target="#dialog"
                                          title="Modifier infos"
                                          aria-label="Modifier">
                                      <div class="mdc-icon-button__ripple"></div>
                                      <span class="material-icons">edit</span>
                                  </button>
                                  <button class="mdc-icon-button show-on-pc"
                                          hx-get="{% url 'school:student_edit' enrollment.id %}?page={{ page }}"
                                          hx-target="#dialog-xl"
                                          title="Modifier infos"
                                          aria-label="Modifier">
                                      <div class="mdc-icon-button__ripple"></div>
                                      <span class="material-icons">edit</span>
                                  </button>

                                  <!-- Payment Button -->
                                  <button class="mdc-icon-button"
                                          hx-get="{% url 'school:payment_add' %}?student_id={{ enrollment.student.identifier }}"
                                          hx-target="#dialog-xl"
                                          title="Ajouter/Modifier un paiement"
                                          aria-label="Paiement">
                                    <div class="mdc-icon-button__ripple"></div>
                                    <span class="material-icons">attach_money</span>
                                  </button>

                                  <!-- Receipt Dropdown 1 -->
                                  <div class="mdc-menu-surface--anchor show-on-pc">
                                      <button class="mdc-icon-button"
                                              title="Reçu Résumé par Rubrique"
                                              aria-label="Reçu 1">
                                          <div class="mdc-icon-button__ripple"></div>
                                          <span class="material-icons">receipt</span>
                                      </button>
                                      <div class="mdc-menu mdc-menu-surface">
                                          <ul class="mdc-list" role="menu">
                                              <li class="mdc-list-item" role="menuitem">
                                                  <a href="{% url 'school:student_payments_pdf' enrollment.id %}?template=2&copies=1"
                                                     target="_blank"
                                                     onclick="Pace.restart()"
                                                     class="mdc-list-item__text">1 copie</a>
                                              </li>
                                              <li class="mdc-list-item" role="menuitem">
                                                  <a href="{% url 'school:student_payments_pdf' enrollment.id %}?template=2&copies=2"
                                                     target="_blank"
                                                     onclick="Pace.restart()"
                                                     class="mdc-list-item__text">2 copies</a>
                                              </li>
                                          </ul>
                                      </div>
                                  </div>

                                  <!-- Delete Button -->
                                  <button class="mdc-icon-button show-on-pc"
                                          hx-get="{% url 'school:student_delete' enrollment.id %}?page={{ page }}&{% if level_fr %}level_fr={{ level_fr }}{% elif level_ar %}level_ar={{ level_ar}}{% endif %}"
                                          hx-target="#dialog"
                                          title="Supprimer élève"
                                          aria-label="Supprimer">
                                      <div class="mdc-icon-button__ripple"></div>
                                      <span class="material-icons">delete</span>
                                  </button>

                                  <!-- Mobile More Menu -->
                                  <div class="mdc-menu-surface--anchor show-on-phone">
                                      <button class="mdc-icon-button" aria-label="Plus d'options">
                                          <div class="mdc-icon-button__ripple"></div>
                                          <span class="material-icons">more_vert</span>
                                      </button>
                                      <div class="mdc-menu mdc-menu-surface">
                                          <ul class="mdc-list" role="menu">
                                              <li class="mdc-list-item" role="menuitem">
                                                  <span class="mdc-list-item__text"
                                                        hx-get="{% url 'school:payment_add' %}?student_id={{ enrollment.student.identifier }}"
                                                        hx-target="#dialog">Paiements</span>
                                              </li>
                                              <li class="mdc-list-divider" role="separator"></li>
                                              <li class="mdc-list-item" role="menuitem">
                                                  <a href="{% url 'school:student_payments_pdf' enrollment.id %}?template=2&copies=1"
                                                     target="_blank"
                                                     onclick="Pace.restart()"
                                                     class="mdc-list-item__text">Reçu Modèle 1</a>
                                              </li>
                                              <li class="mdc-list-item" role="menuitem">
                                                  <a href="{% url 'school:student_payments_pdf' enrollment.id %}?copies=1&template=1"
                                                     target="_blank"
                                                     onclick="Pace.restart()"
                                                     class="mdc-list-item__text">Reçu Modèle 2</a>
                                              </li>
                                              <li class="mdc-list-divider" role="separator"></li>
                                              <li class="mdc-list-item" role="menuitem">
                                                  <span class="mdc-list-item__text"
                                                        hx-get="{% url 'school:student_delete' enrollment.id %}?page={{ page }}&{% if level_fr %}level_fr={{ level_fr }}{% elif level_ar %}level_ar={{ level_ar}}{% endif %}"
                                                        hx-target="#dialog">Supprimer élève</span>
                                              </li>
                                          </ul>
                                      </div>
                                  </div>

                                  {% else %}
                                    <!-- Inactive Student - Enroll Button -->
                                    <button class="mdc-button mdc-button--raised show-on-pc"
                                            hx-get="{% url 'school:student_edit' enrollment.id %}?page={{ page }}&{% if level_fr %}level_fr={{ level_fr }}{% elif level_ar %}level_ar={{ level_ar}}{% endif %}"
                                            hx-target="#dialog-xl">
                                        <span class="mdc-button__ripple"></span>
                                        <span class="material-icons mdc-button__icon">person_add</span>
                                        <span class="mdc-button__label">Inscrire</span>
                                    </button>
                                    <button class="mdc-button mdc-button--raised show-on-phone"
                                            hx-get="{% url 'school:student_edit_wizard' enrollment.id %}?page={{ page }}&{% if level_fr %}level_fr={{ level_fr }}{% elif level_ar %}level_ar={{ level_ar}}{% endif %}"
                                            hx-target="#dialog">
                                        <span class="mdc-button__ripple"></span>
                                        <span class="material-icons mdc-button__icon">person_add</span>
                                        <span class="mdc-button__label">Inscrire</span>
                                    </button>
                                  {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Data Table Pagination -->
            <div class="mdc-data-table__pagination">
                <div class="mdc-data-table__pagination-trailing">
                    <div class="mdc-data-table__pagination-rows-per-page">
                        <div class="mdc-data-table__pagination-rows-per-page-label">
                            Lignes par page
                        </div>

                        <div class="mdc-select mdc-select--outlined mdc-select--no-label mdc-data-table__pagination-rows-per-page-select">
                            <select class="mdc-select__native-control"
                                    name="per_page"
                                    hx-get="{{ request.path }}"
                                    hx-target="#app-content"
                                    hx-vals='{"statut": "{{ statut_inscrit }}"}'>
                                <option value="10" {% if per_page == '10' %} selected {% endif %}>10</option>
                                <option value="25" {% if per_page == '25' %} selected {% endif %}>25</option>
                                <option value="50" {% if per_page == '50' %} selected {% endif %}>50</option>
                                <option value="100" {% if per_page == '100' %} selected {% endif %}>100</option>
                            </select>
                            <span class="mdc-notched-outline">
                                <span class="mdc-notched-outline__leading"></span>
                                <span class="mdc-notched-outline__trailing"></span>
                            </span>
                        </div>
                    </div>

                    <div class="mdc-data-table__pagination-navigation">
                        <div class="mdc-data-table__pagination-total">
                            {% if enrollments %}{{ enrollments.start_index }}-{{ enrollments.end_index }} de {{ enrollments.paginator.count }}{% endif %}
                        </div>
                        {% if enrollments.has_previous %}
                        <button class="mdc-icon-button material-icons mdc-data-table__pagination-button"
                                hx-get="{{ request.path }}?page=1"
                                hx-target="#app-content"
                                hx-include="[name='per_page']">
                            <div class="mdc-button__icon">first_page</div>
                        </button>
                        <button class="mdc-icon-button material-icons mdc-data-table__pagination-button"
                                hx-get="{{ request.path }}?page={{ enrollments.previous_page_number }}"
                                hx-target="#app-content"
                                hx-include="[name='per_page']">
                            <div class="mdc-button__icon">chevron_left</div>
                        </button>
                        {% else %}
                        <button class="mdc-icon-button material-icons mdc-data-table__pagination-button" disabled>
                            <div class="mdc-button__icon">first_page</div>
                        </button>
                        <button class="mdc-icon-button material-icons mdc-data-table__pagination-button" disabled>
                            <div class="mdc-button__icon">chevron_left</div>
                        </button>
                        {% endif %}

                        {% if enrollments.has_next %}
                        <button class="mdc-icon-button material-icons mdc-data-table__pagination-button"
                                hx-get="{{ request.path }}?page={{ enrollments.next_page_number }}"
                                hx-target="#app-content"
                                hx-include="[name='per_page']">
                            <div class="mdc-button__icon">chevron_right</div>
                        </button>
                        <button class="mdc-icon-button material-icons mdc-data-table__pagination-button"
                                hx-get="{{ request.path }}?page={{ enrollments.paginator.num_pages }}"
                                hx-target="#app-content"
                                hx-include="[name='per_page']">
                            <div class="mdc-button__icon">last_page</div>
                        </button>
                        {% else %}
                        <button class="mdc-icon-button material-icons mdc-data-table__pagination-button" disabled>
                            <div class="mdc-button__icon">chevron_right</div>
                        </button>
                        <button class="mdc-icon-button material-icons mdc-data-table__pagination-button" disabled>
                            <div class="mdc-button__icon">last_page</div>
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
  </div>

  <!-- Include student actions and JavaScript -->
  {% include 'partials/student/actions.html' %}
</div>
{% include 'partials/active_students/students_list_js.html' %}