<div class="page-content" id="page-content">
    <h1>System Signals</h1>

    <!-- Enhanced Material Design Data Table -->
    <div class="mdc-card data-table-card mdc-card--outlined">
        <!-- Data Table Header with Actions -->
        <div class="data-table-header">
            <!-- Filter Toolbar -->
            <div class="filter-toolbar">
                <button class="mdc-icon-button" aria-label="Filter">
                    <div class="mdc-icon-button__ripple"></div>
                    <span class="material-icons">filter_list</span>
                </button>

                <!-- Filter Chips -->
                <div class="mdc-chip-set" role="grid">
                    <div class="mdc-chip" role="row">
                        <div class="mdc-chip__ripple"></div>
                        <span class="mdc-chip__icon mdc-chip__icon--leading material-icons">check_circle</span>
                        <span class="mdc-chip__text">Offline</span>
                        <button class="mdc-chip__icon mdc-chip__icon--trailing material-icons" tabindex="-1">cancel</button>
                    </div>
                    <div class="mdc-chip" role="row">
                        <div class="mdc-chip__ripple"></div>
                        <span class="mdc-chip__icon mdc-chip__icon--leading material-icons">schedule</span>
                        <span class="mdc-chip__text">Time ≥ 10min</span>
                        <button class="mdc-chip__icon mdc-chip__icon--trailing material-icons" tabindex="-1">cancel</button>
                    </div>
                </div>
            </div>

            <!-- Data Table Actions -->
            <div class="data-table-actions">
                <!-- Search Field -->
                <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--with-leading-icon search-field">
                    <span class="material-icons mdc-text-field__icon mdc-text-field__icon--leading">search</span>
                    <input id="data-table-search" class="mdc-text-field__input" type="text" placeholder="Search signals...">
                    <div class="mdc-notched-outline">
                        <div class="mdc-notched-outline__leading"></div>
                        <div class="mdc-notched-outline__notch">
                            <label class="mdc-floating-label">Search</label>
                        </div>
                        <div class="mdc-notched-outline__trailing"></div>
                    </div>
                </div>

                <!-- More Options Button -->
                <button class="mdc-icon-button" aria-label="More options">
                    <div class="mdc-icon-button__ripple"></div>
                    <span class="material-icons">more_vert</span>
                </button>
            </div>
        </div>

        <!-- Data Table -->
        <div class="mdc-data-table">
            <div class="mdc-data-table__table-container">
                <table class="mdc-data-table__table">
                    <thead>
                        <tr class="mdc-data-table__header-row">
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--checkbox">
                                <div class="mdc-checkbox mdc-checkbox--selected mdc-data-table__header-row-checkbox">
                                    <input type="checkbox" class="mdc-checkbox__native-control" aria-label="Toggle all rows"/>
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                </div>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort" role="columnheader" scope="col" aria-sort="none">
                                <div class="mdc-data-table__header-cell-wrapper">
                                    <div class="mdc-data-table__header-cell-label">Status</div>
                                    <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" aria-label="Sort by Status">arrow_upward</button>
                                </div>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort" role="columnheader" scope="col" aria-sort="none" style="width: 200px">
                                <div class="mdc-data-table__header-cell-wrapper">
                                    <div class="mdc-data-table__header-cell-label">Signal Name</div>
                                    <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" aria-label="Sort by Signal Name">arrow_upward</button>
                                </div>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort" role="columnheader" scope="col" aria-sort="none">
                                <div class="mdc-data-table__header-cell-wrapper">
                                    <div class="mdc-data-table__header-cell-label">Severity</div>
                                    <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" aria-label="Sort by Severity">arrow_upward</button>
                                </div>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort" role="columnheader" scope="col" aria-sort="none">
                                <div class="mdc-data-table__header-cell-wrapper">
                                    <div class="mdc-data-table__header-cell-label">Stage</div>
                                    <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" aria-label="Sort by Stage">arrow_upward</button>
                                </div>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort" role="columnheader" scope="col" aria-sort="none">
                                <div class="mdc-data-table__header-cell-wrapper">
                                    <div class="mdc-data-table__header-cell-label">Lapsed Time</div>
                                    <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" aria-label="Sort by Lapsed Time">arrow_upward</button>
                                </div>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort" role="columnheader" scope="col" aria-sort="none">
                                <div class="mdc-data-table__header-cell-wrapper">
                                    <div class="mdc-data-table__header-cell-label">Team Lead</div>
                                    <button class="mdc-icon-button material-icons mdc-data-table__sort-icon-button" aria-label="Sort by Team Lead">arrow_upward</button>
                                </div>
                            </th>
                            <th class="mdc-data-table__header-cell">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="mdc-data-table__content">
                        <tr class="mdc-data-table__row">
                            <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                    <input type="checkbox" class="mdc-checkbox__native-control" aria-labelledby="u0"/>
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="mdc-data-table__cell">Offline</td>
                            <td class="mdc-data-table__cell">Astrid: NE shared managed-features</td>
                            <td class="mdc-data-table__cell"><span class="severity-medium">Medium</span></td>
                            <td class="mdc-data-table__cell">Triaged</td>
                            <td class="mdc-data-table__cell">10:12</td>
                            <td class="mdc-data-table__cell">Chase Nguyen</td>
                            <td class="mdc-data-table__cell">
                                <div class="mdc-data-table__row-actions">
                                    <button class="mdc-icon-button" aria-label="Edit">
                                        <div class="mdc-icon-button__ripple"></div><span class="material-icons">edit</span>
                                    </button>
                                    <button class="mdc-icon-button" aria-label="Delete">
                                        <div class="mdc-icon-button__ripple"></div><span class="material-icons">delete</span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="mdc-data-table__row">
                            <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                    <input type="checkbox" class="mdc-checkbox__native-control" aria-labelledby="u1"/>
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="mdc-data-table__cell">Offline</td>
                            <td class="mdc-data-table__cell">Cosmo: prod shared vm</td>
                            <td class="mdc-data-table__cell"><span class="severity-huge">Huge</span></td>
                            <td class="mdc-data-table__cell">Triaged</td>
                            <td class="mdc-data-table__cell">12:45</td>
                            <td class="mdc-data-table__cell">Brie Furman</td>
                            <td class="mdc-data-table__cell">
                                <div class="mdc-data-table__row-actions">
                                    <button class="mdc-icon-button" aria-label="Edit">
                                        <div class="mdc-icon-button__ripple"></div><span class="material-icons">edit</span>
                                    </button>
                                    <button class="mdc-icon-button" aria-label="Delete">
                                        <div class="mdc-icon-button__ripple"></div><span class="material-icons">delete</span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="mdc-data-table__row">
                            <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                    <input type="checkbox" class="mdc-checkbox__native-control" aria-labelledby="u2"/>
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="mdc-data-table__cell">Offline</td>
                            <td class="mdc-data-table__cell">Phoenix: prod shared lyra-managed-features</td>
                            <td class="mdc-data-table__cell"><span class="severity-minor">Minor</span></td>
                            <td class="mdc-data-table__cell">Triaged</td>
                            <td class="mdc-data-table__cell">13:06</td>
                            <td class="mdc-data-table__cell">Jeremy Lake</td>
                            <td class="mdc-data-table__cell">
                                <div class="mdc-data-table__row-actions">
                                    <button class="mdc-icon-button" aria-label="Edit">
                                        <div class="mdc-icon-button__ripple"></div><span class="material-icons">edit</span>
                                    </button>
                                    <button class="mdc-icon-button" aria-label="Delete">
                                        <div class="mdc-icon-button__ripple"></div><span class="material-icons">delete</span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="mdc-data-table__row">
                            <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                    <input type="checkbox" class="mdc-checkbox__native-control" aria-labelledby="u3"/>
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="mdc-data-table__cell">Offline</td>
                            <td class="mdc-data-table__cell">Sirius: prod shared ares-managed-vm</td>
                            <td class="mdc-data-table__cell"><span class="severity-negligible">Negligible</span></td>
                            <td class="mdc-data-table__cell">Triaged</td>
                            <td class="mdc-data-table__cell">13:18</td>
                            <td class="mdc-data-table__cell">Angelica Howards</td>
                            <td class="mdc-data-table__cell">
                                <div class="mdc-data-table__row-actions">
                                    <button class="mdc-icon-button" aria-label="Edit">
                                        <div class="mdc-icon-button__ripple"></div><span class="material-icons">edit</span>
                                    </button>
                                    <button class="mdc-icon-button" aria-label="Delete">
                                        <div class="mdc-icon-button__ripple"></div><span class="material-icons">delete</span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Data Table Pagination -->
            <div class="mdc-data-table__pagination">
                <div class="mdc-data-table__pagination-trailing">
                    <div class="mdc-data-table__pagination-rows-per-page">
                        <div class="mdc-data-table__pagination-rows-per-page-label">
                            Rows per page
                        </div>

                        <div class="mdc-select mdc-select--outlined mdc-select--no-label mdc-data-table__pagination-rows-per-page-select">
                            <div class="mdc-select__anchor" role="button" aria-haspopup="listbox">
                                <span class="mdc-select__selected-text">4</span>
                                <span class="mdc-select__dropdown-icon">
                                    <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                        <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                        <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                    </svg>
                                </span>
                                <span class="mdc-notched-outline">
                                    <span class="mdc-notched-outline__leading"></span>
                                    <span class="mdc-notched-outline__trailing"></span>
                                </span>
                            </div>

                            <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                                <ul class="mdc-list" role="listbox">
                                    <li class="mdc-list-item mdc-list-item--selected" aria-selected="true" role="option" data-value="4">
                                        <span class="mdc-list-item__text">4</span>
                                    </li>
                                    <li class="mdc-list-item" role="option" data-value="8">
                                        <span class="mdc-list-item__text">8</span>
                                    </li>
                                    <li class="mdc-list-item" role="option" data-value="12">
                                        <span class="mdc-list-item__text">12</span>
                                    </li>
                                    <li class="mdc-list-item" role="option" data-value="16">
                                        <span class="mdc-list-item__text">16</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="mdc-data-table__pagination-navigation">
                        <div class="mdc-data-table__pagination-total">
                            1-4 of 100
                        </div>
                        <button class="mdc-icon-button material-icons mdc-data-table__pagination-button" data-page="first" disabled>
                            <div class="mdc-button__icon">first_page</div>
                        </button>
                        <button class="mdc-icon-button material-icons mdc-data-table__pagination-button" data-page="prev" disabled>
                            <div class="mdc-button__icon">chevron_left</div>
                        </button>
                        <button class="mdc-icon-button material-icons mdc-data-table__pagination-button" data-page="next">
                            <div class="mdc-button__icon">chevron_right</div>
                        </button>
                        <button class="mdc-icon-button material-icons mdc-data-table__pagination-button" data-page="last">
                            <div class="mdc-button__icon">last_page</div>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>