from django.contrib.auth import views as auth_views, get_user_model
from django.conf import settings
from django.shortcuts import render, redirect
from django.urls import reverse, reverse_lazy
from django.views import View
from django.contrib import messages
from django.contrib.auth.forms import SetPasswordForm
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_protect
from django.views.decorators.debug import sensitive_post_parameters
from django.http import HttpResponseRedirect, HttpResponse

from .models import VerificationCode
from .forms import PhoneVerificationForm, VerifyCodeForm
from main.sms import send_sms

User = get_user_model()

class MaterialPasswordResetView(View):
    """
    Custom password reset view that uses phone verification.
    """
    template_name = settings.PASSWORD_RESET_TEMPLATE_NAME
    base_template = 'material/auth/password_reset/base.html'
    partial_template = 'material/auth/password_reset/partials/phone_form.html'
    form_class = PhoneVerificationForm

    def get(self, request):
        form = self.form_class()
        context = {'form': form, 'partial_template': self.partial_template}

        # Check if this is an HTMX request
        if request.headers.get('HX-Request'):
            return render(request, self.partial_template, context)

        return render(request, self.base_template, context)

    def post(self, request):
        form = self.form_class(request.POST)
        if form.is_valid():
            phone = form.cleaned_data['phone']
            try:
                user = User.objects.get(phone=phone)
                # Generate verification code
                verification_code = VerificationCode.generate_code(user)

                # Send SMS with verification code
                self.send_verification_sms(user, verification_code.code)

                # Store user_id in session for the next step
                request.session['reset_user_id'] = user.id

                # If HTMX request, render the verify code partial
                if request.headers.get('HX-Request'):
                    verify_view = MaterialPasswordResetVerifyView()
                    return verify_view.get(request)

                # Otherwise redirect to verification page
                return redirect('users:password_reset_verify')
            except User.DoesNotExist:
                form.add_error('phone', "Aucun compte n'est associé à ce numéro de téléphone.")

        context = {'form': form, 'partial_template': self.partial_template}

        # If HTMX request, render only the form partial
        if request.headers.get('HX-Request'):
            return render(request, self.partial_template, context)

        return render(request, self.base_template, context)

    def send_verification_sms(self, user, code):
        """Send SMS with verification code."""
        print(f"Sending verification code to {user.phone}")

        send_sms(
            session_key=self.request.session.session_key,
            to=f'+225{user.phone}',
            message=f"Votre code de vérification EcolePro est: {code}. Il expire dans 15 minutes."
        )


class MaterialPasswordResetVerifyView(View):
    """
    View to verify the SMS code.
    """
    template_name = settings.PASSWORD_RESET_VERIFY_TEMPLATE_NAME
    base_template = 'material/auth/password_reset/base.html'
    partial_template = 'material/auth/password_reset/partials/verify_code.html'
    form_class = VerifyCodeForm

    def get(self, request):
        if 'reset_user_id' not in request.session:
            # If HTMX request, render the phone form partial
            if request.headers.get('HX-Request'):
                reset_view = MaterialPasswordResetView()
                return reset_view.get(request)
            return redirect('users:password_reset')

        form = self.form_class()
        context = {'form': form, 'partial_template': self.partial_template}

        # Check if this is an HTMX request
        if request.headers.get('HX-Request'):
            return render(request, self.partial_template, context)

        return render(request, self.base_template, context)

    def post(self, request):
        if 'reset_user_id' not in request.session:
            # If HTMX request, render the phone form partial
            if request.headers.get('HX-Request'):
                reset_view = MaterialPasswordResetView()
                return reset_view.get(request)
            return redirect('users:password_reset')

        form = self.form_class(request.POST)
        if form.is_valid():
            code = form.cleaned_data['code']
            user_id = request.session['reset_user_id']

            try:
                user = User.objects.get(id=user_id)
                verification = VerificationCode.objects.filter(
                    user=user,
                    code=code,
                    is_used=False
                ).order_by('-created_at').first()

                if verification and verification.is_valid():
                    # Mark code as used
                    verification.is_used = True
                    verification.save()

                    # Store verification ID in session for the next step
                    request.session['verification_id'] = verification.id

                    # If HTMX request, render the new password partial
                    if request.headers.get('HX-Request'):
                        confirm_view = MaterialPasswordResetConfirmView()
                        return confirm_view.get(request)

                    # Otherwise redirect to confirm page
                    return redirect('users:password_reset_confirm')
                else:
                    form.add_error('code', "Code invalide ou expiré. Veuillez réessayer.")
            except User.DoesNotExist:
                # If HTMX request, render the phone form partial
                if request.headers.get('HX-Request'):
                    reset_view = MaterialPasswordResetView()
                    return reset_view.get(request)
                return redirect('users:password_reset')

        context = {'form': form, 'partial_template': self.partial_template}

        # If HTMX request, render only the form partial
        if request.headers.get('HX-Request'):
            return render(request, self.partial_template, context)

        return render(request, self.base_template, context)


class MaterialPasswordResetConfirmView(View):
    """
    View to set a new password after verification.
    """
    template_name = settings.PASSWORD_RESET_CONFIRM_TEMPLATE_NAME
    base_template = 'material/auth/password_reset/base.html'
    partial_template = 'material/auth/password_reset/partials/new_password.html'
    form_class = SetPasswordForm
    success_url = reverse_lazy('users:password_reset_complete')

    @method_decorator(sensitive_post_parameters())
    @method_decorator(csrf_protect)
    def dispatch(self, *args, **kwargs):
        return super().dispatch(*args, **kwargs)

    def get(self, request):
        if 'reset_user_id' not in request.session or 'verification_id' not in request.session:
            # If HTMX request, render the phone form partial
            if request.headers.get('HX-Request'):
                reset_view = MaterialPasswordResetView()
                return reset_view.get(request)
            return redirect('users:password_reset')

        user_id = request.session['reset_user_id']
        verification_id = request.session['verification_id']

        try:
            user = User.objects.get(id=user_id)
            # Just check if the verification exists and is valid
            VerificationCode.objects.get(id=verification_id, user=user, is_used=True)

            form = self.form_class(user)
            context = {
                'form': form,
                'validlink': True,
                'partial_template': self.partial_template
            }

            # Check if this is an HTMX request
            if request.headers.get('HX-Request'):
                return render(request, self.partial_template, context)

            return render(request, self.base_template, context)
        except (User.DoesNotExist, VerificationCode.DoesNotExist):
            context = {'validlink': False, 'partial_template': self.partial_template}

            # Check if this is an HTMX request
            if request.headers.get('HX-Request'):
                return render(request, self.partial_template, context)

            return render(request, self.base_template, context)

    def post(self, request):
        if 'reset_user_id' not in request.session or 'verification_id' not in request.session:
            # If HTMX request, render the phone form partial
            if request.headers.get('HX-Request'):
                reset_view = MaterialPasswordResetView()
                return reset_view.get(request)
            return redirect('users:password_reset')

        user_id = request.session['reset_user_id']
        verification_id = request.session['verification_id']

        try:
            user = User.objects.get(id=user_id)
            # Just check if the verification exists and is valid
            VerificationCode.objects.get(id=verification_id, user=user, is_used=True)

            form = self.form_class(user, request.POST)
            if form.is_valid():
                form.save()

                # Update custom_password field
                user.custom_password = form.cleaned_data['new_password1']
                user.save(update_fields=['custom_password'])

                # Clear session data
                if 'reset_user_id' in request.session:
                    del request.session['reset_user_id']
                if 'verification_id' in request.session:
                    del request.session['verification_id']

                # If HTMX request, render the complete partial
                if request.headers.get('HX-Request'):
                    complete_view = MaterialPasswordResetCompleteView()
                    return complete_view.get(request)

                # Otherwise redirect to complete page
                return HttpResponseRedirect(self.success_url)

            context = {
                'form': form,
                'validlink': True,
                'partial_template': self.partial_template
            }

            # If HTMX request, render only the form partial
            if request.headers.get('HX-Request'):
                return render(request, self.partial_template, context)

            return render(request, self.base_template, context)
        except (User.DoesNotExist, VerificationCode.DoesNotExist):
            context = {'validlink': False, 'partial_template': self.partial_template}

            # If HTMX request, render only the form partial
            if request.headers.get('HX-Request'):
                return render(request, self.partial_template, context)

            return render(request, self.base_template, context)


class MaterialPasswordResetDoneView(View):
    """
    Custom password reset done view that uses Material Design template.
    """
    template_name = settings.PASSWORD_RESET_DONE_TEMPLATE_NAME
    base_template = 'material/auth/password_reset/base.html'
    partial_template = 'material/auth/password_reset/partials/complete.html'

    def get(self, request):
        context = {'partial_template': self.partial_template}

        # Check if this is an HTMX request
        if request.headers.get('HX-Request'):
            return render(request, self.partial_template, context)

        return render(request, self.base_template, context)


class MaterialPasswordResetCompleteView(View):
    """
    Custom password reset complete view that uses Material Design template.
    """
    template_name = settings.PASSWORD_RESET_COMPLETE_TEMPLATE_NAME
    base_template = 'material/auth/password_reset/base.html'
    partial_template = 'material/auth/password_reset/partials/complete.html'

    def get(self, request):
        context = {'partial_template': self.partial_template}

        # Check if this is an HTMX request
        if request.headers.get('HX-Request'):
            return render(request, self.partial_template, context)

        return render(request, self.base_template, context)
